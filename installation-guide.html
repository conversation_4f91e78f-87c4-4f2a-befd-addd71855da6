<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل التثبيت - نظام الصفوة للنسيج</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        
        .step {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-right: 5px solid #2563eb;
            position: relative;
            overflow: hidden;
        }
        
        .step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #2563eb, #06b6d4);
        }
        
        .step-number {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
        }
        
        .step-title {
            font-size: 20px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .step-desc {
            color: #4b5563;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(37, 99, 235, 0.4);
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
            border: 1px solid #374151;
        }
        
        .success-box {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .success-title {
            color: #065f46;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .success-desc {
            color: #047857;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning-text {
            color: #92400e;
            font-weight: 600;
        }
        
        .progress-bar {
            background: #e5e7eb;
            height: 8px;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #2563eb, #06b6d4);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        
        .accounts-section {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .accounts-title {
            color: #5b21b6;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .account {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 10px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .account-role {
            font-weight: 600;
            color: #5b21b6;
        }
        
        .account-creds {
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏢 شركة الصفوة للنسيج</div>
            <div class="subtitle">دليل تثبيت نظام إدارة موارد المؤسسة</div>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>
        
        <!-- Step 1: Node.js -->
        <div class="step" id="step1">
            <div class="step-number">1</div>
            <div class="step-title">تثبيت Node.js</div>
            <div class="step-desc">
                Node.js هو بيئة تشغيل JavaScript مطلوبة لتشغيل النظام. حمل النسخة LTS (الموصى بها).
            </div>
            <div class="warning-box">
                <div class="warning-text">⚠️ مهم: اختر النسخة LTS للاستقرار</div>
            </div>
            <a href="https://nodejs.org/" class="download-btn" target="_blank" onclick="updateProgress(33)">
                📥 تحميل Node.js
            </a>
        </div>
        
        <!-- Step 2: PostgreSQL -->
        <div class="step" id="step2">
            <div class="step-number">2</div>
            <div class="step-title">تثبيت PostgreSQL</div>
            <div class="step-desc">
                PostgreSQL هو نظام إدارة قواعد البيانات المطلوب لحفظ بيانات النظام.
            </div>
            <div class="warning-box">
                <div class="warning-text">⚠️ مهم: احفظ كلمة مرور المستخدم postgres</div>
            </div>
            <a href="https://www.postgresql.org/download/windows/" class="download-btn" target="_blank" onclick="updateProgress(66)">
                📥 تحميل PostgreSQL
            </a>
        </div>
        
        <!-- Step 3: Run System -->
        <div class="step" id="step3">
            <div class="step-number">3</div>
            <div class="step-title">تشغيل النظام</div>
            <div class="step-desc">
                بعد تثبيت Node.js و PostgreSQL، شغل الأوامر التالية في Command Prompt:
            </div>
            <div class="code-block">
npm install<br>
copy .env.example .env<br>
npm run dev
            </div>
            <button class="download-btn" onclick="updateProgress(100); showSuccess()">
                ✅ تم التثبيت
            </button>
        </div>
        
        <!-- Success Message -->
        <div class="success-box" id="successBox" style="display: none;">
            <div class="success-title">🎉 تم التثبيت بنجاح!</div>
            <div class="success-desc">
                النظام جاهز للتشغيل على: <strong>http://localhost:3000</strong>
            </div>
        </div>
        
        <!-- Demo Accounts -->
        <div class="accounts-section">
            <div class="accounts-title">🔐 الحسابات التجريبية</div>
            
            <div class="account">
                <div class="account-role">مدير النظام</div>
                <div class="account-creds"><EMAIL> / admin123</div>
            </div>
            
            <div class="account">
                <div class="account-role">مشرف الإنتاج</div>
                <div class="account-creds"><EMAIL> / super123</div>
            </div>
            
            <div class="account">
                <div class="account-role">مشغل الماكينة</div>
                <div class="account-creds"><EMAIL> / oper123</div>
            </div>
        </div>
    </div>
    
    <script>
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }
        
        function showSuccess() {
            document.getElementById('successBox').style.display = 'block';
            document.getElementById('successBox').scrollIntoView({ behavior: 'smooth' });
        }
        
        // Auto-update progress based on completed steps
        let completedSteps = 0;
        
        function markStepComplete(stepNumber) {
            completedSteps++;
            updateProgress((completedSteps / 3) * 100);
            
            if (completedSteps === 3) {
                showSuccess();
            }
        }
    </script>
</body>
</html>
