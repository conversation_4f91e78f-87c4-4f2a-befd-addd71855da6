'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDate, formatCurrency } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, Phone, Mail, MapPin, User, Star } from 'lucide-react'

// بيانات تجريبية للعملاء
const mockCustomers = [
  {
    id: '1',
    code: 'CUS001',
    nameAr: 'متجر الأزياء الراقية',
    nameEn: 'Luxury Fashion Store',
    type: 'retail',
    contactPerson: 'فاطمة علي',
    phone: '+966503456789',
    email: '<EMAIL>',
    address: 'الدمام، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 28,
    totalAmount: 89000,
    lastOrderDate: new Date('2024-01-22'),
    creditLimit: 50000,
    isVip: true,
    registrationDate: new Date('2023-06-15'),
  },
  {
    id: '2',
    code: 'CUS002',
    nameAr: 'شركة الملابس التجارية',
    nameEn: 'Commercial Clothing Company',
    type: 'wholesale',
    contactPerson: 'محمد السعد',
    phone: '+966504567890',
    email: '<EMAIL>',
    address: 'الرياض، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 45,
    totalAmount: 156000,
    lastOrderDate: new Date('2024-01-20'),
    creditLimit: 100000,
    isVip: true,
    registrationDate: new Date('2023-03-10'),
  },
  {
    id: '3',
    code: 'CUS003',
    nameAr: 'بوتيك الأناقة',
    nameEn: 'Elegance Boutique',
    type: 'retail',
    contactPerson: 'نورا أحمد',
    phone: '+966505678901',
    email: '<EMAIL>',
    address: 'جدة، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 18,
    totalAmount: 34000,
    lastOrderDate: new Date('2024-01-18'),
    creditLimit: 25000,
    isVip: false,
    registrationDate: new Date('2023-09-22'),
  },
  {
    id: '4',
    code: 'CUS004',
    nameAr: 'مؤسسة الزي الموحد',
    nameEn: 'Uniform Corporation',
    type: 'corporate',
    contactPerson: 'خالد المطيري',
    phone: '+966506789012',
    email: '<EMAIL>',
    address: 'الخبر، المملكة العربية السعودية',
    isActive: false,
    totalOrders: 8,
    totalAmount: 12000,
    lastOrderDate: new Date('2023-12-05'),
    creditLimit: 30000,
    isVip: false,
    registrationDate: new Date('2023-11-01'),
  },
]

const typeLabels = {
  retail: 'تجزئة',
  wholesale: 'جملة',
  corporate: 'شركات',
  vip: 'مميز',
}

export function CustomersList() {
  const [customers] = useState(mockCustomers)

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'retail':
        return 'bg-blue-100 text-blue-800'
      case 'wholesale':
        return 'bg-purple-100 text-purple-800'
      case 'corporate':
        return 'bg-green-100 text-green-800'
      case 'vip':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800'
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الكود</TableHead>
            <TableHead>اسم العميل</TableHead>
            <TableHead>النوع</TableHead>
            <TableHead>الشخص المسؤول</TableHead>
            <TableHead>معلومات الاتصال</TableHead>
            <TableHead>إجمالي الطلبات</TableHead>
            <TableHead>آخر طلب</TableHead>
            <TableHead>الحد الائتماني</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  {customer.code}
                  {customer.isVip && (
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium flex items-center gap-2">
                    {customer.nameAr}
                    {customer.isVip && (
                      <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                        VIP
                      </Badge>
                    )}
                  </div>
                  {customer.nameEn && (
                    <div className="text-sm text-muted-foreground">
                      {customer.nameEn}
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getTypeColor(customer.type)}>
                  {typeLabels[customer.type as keyof typeof typeLabels]}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="text-sm font-medium">{customer.contactPerson}</div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-sm">
                    <Phone className="h-3 w-3" />
                    {customer.phone}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <Mail className="h-3 w-3" />
                    {customer.email}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    {customer.address}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{customer.totalOrders} طلب</div>
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(customer.totalAmount)}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {formatDate(customer.lastOrderDate)}
              </TableCell>
              <TableCell>
                <div className="font-medium">
                  {formatCurrency(customer.creditLimit)}
                </div>
                <div className="text-xs text-muted-foreground">
                  متاح: {formatCurrency(customer.creditLimit - (customer.totalAmount * 0.1))}
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(customer.isActive)}>
                  {customer.isActive ? 'نشط' : 'غير نشط'}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Eye className="ml-2 h-4 w-4" />
                      عرض التفاصيل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Phone className="ml-2 h-4 w-4" />
                      اتصال
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Mail className="ml-2 h-4 w-4" />
                      إرسال بريد
                    </DropdownMenuItem>
                    {!customer.isVip && (
                      <DropdownMenuItem>
                        <Star className="ml-2 h-4 w-4" />
                        ترقية لـ VIP
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="ml-2 h-4 w-4" />
                      حذف
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
