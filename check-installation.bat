@echo off
chcp 65001 >nul
echo.
echo 🔍 فحص التثبيت - نظام الصفوة للنسيج
echo =====================================
echo.

echo ℹ️  فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js مثبت: !NODE_VERSION!
) else (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل Node.js من: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo.
echo ℹ️  فحص npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm مثبت: !NPM_VERSION!
) else (
    echo ❌ npm غير مثبت
    pause
    exit /b 1
)

echo.
echo ℹ️  فحص PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('psql --version') do set PSQL_VERSION=%%i
    echo ✅ PostgreSQL مثبت: !PSQL_VERSION!
) else (
    echo ⚠️  PostgreSQL غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تحميل PostgreSQL من: https://www.postgresql.org/download/windows/
    echo.
    echo ⏭️  يمكنك المتابعة وإعداد PostgreSQL لاحقاً
)

echo.
echo =====================================
echo ✅ فحص التثبيت مكتمل!
echo.

if exist "package.json" (
    echo 🚀 جاهز لتشغيل النظام!
    echo.
    echo الخطوات التالية:
    echo 1. npm install
    echo 2. copy .env.example .env
    echo 3. npm run dev
    echo.
    echo هل تريد تشغيل النظام الآن؟ (y/n)
    set /p runNow=
    if "!runNow!"=="y" (
        echo.
        echo 📦 تثبيت المكتبات...
        npm install
        
        echo.
        echo 📄 إنشاء ملف البيئة...
        if not exist ".env" (
            if exist ".env.example" (
                copy ".env.example" ".env" >nul
                echo ✅ تم إنشاء ملف .env
            )
        )
        
        echo.
        echo 🚀 تشغيل النظام...
        echo النظام سيعمل على: http://localhost:3000
        echo.
        npm run dev
    )
) else (
    echo ⚠️  ملف package.json غير موجود
    echo تأكد من أنك في مجلد المشروع الصحيح
)

echo.
pause
