@echo off
title إنشاء مثبت نظام الصفوة EXE - مبسط
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء مثبت نظام الصفوة EXE - مبسط
echo                              📦 ملف EXE واحد شامل 📦
echo.
echo ===============================================================================
echo.

echo مرحباً بك في منشئ مثبت نظام الصفوة EXE!
echo.
echo هذا المنشئ سيقوم بإنشاء ملف EXE واحد يحتوي على:
echo ✓ مثبت نظام الصفوة الكامل
echo ✓ واجهة تثبيت احترافية
echo ✓ جميع الملفات مدمجة
echo ✓ سهولة التوزيع والتشغيل
echo.

echo [1/6] إنشاء مجلد المثبت...
set INSTALLER_NAME=تثبيت-نظام-الصفوة
if exist "%INSTALLER_NAME%" rmdir /s /q "%INSTALLER_NAME%"
mkdir "%INSTALLER_NAME%"

echo [2/6] إنشاء ملف التثبيت الرئيسي...
(
echo @echo off
echo title مثبت نظام الصفوة للنسيج
echo color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           مثبت نظام الصفوة للنسيج
echo                              📦 مثبت احترافي شامل 📦
echo.
echo ===============================================================================
echo.
echo مرحباً بك في مثبت نظام الصفوة لإدارة مصانع النسيج!
echo.
echo معلومات البرنامج:
echo • الاسم: نظام الصفوة لإدارة مصانع النسيج
echo • الإصدار: 1.0.0
echo • المطور: شركة الصفوة للنسيج
echo • النوع: نظام ERP متكامل
echo.
echo الوحدات المتاحة:
echo ✓ الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo ✓ المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo ✓ المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo ✓ المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo ✓ المالية: الحسابات، الفواتير، التقارير المالية
echo ✓ الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo ✓ أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo المميزات:
echo ✓ يعمل بدون إنترنت تماماً
echo ✓ واجهة عربية احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ بيانات تجريبية واقعية
echo ✓ أداء فائق وسرعة عالية
echo.
echo هل تريد المتابعة مع التثبيت؟ ^(Y/N^)
set /p confirm="اكتب Y للمتابعة أو N للإلغاء: "
echo.
if /i "%%confirm%%" neq "Y" ^(
    echo تم إلغاء التثبيت.
    pause
    exit /b 0
^)
echo.
echo ===============================================
echo بدء تثبيت نظام الصفوة للنسيج
echo ===============================================
echo.
echo [1/12] فحص متطلبات النظام...
echo فحص نظام التشغيل...
ver ^| find "Windows" ^>nul
if errorlevel 1 ^(
    echo ✗ هذا البرنامج يعمل على Windows فقط
    pause
    exit /b 1
^)
echo ✓ نظام التشغيل متوافق
echo.
echo فحص Node.js...
node --version ^>nul 2^>^&1
if errorlevel 1 ^(
    echo ✗ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js أولاً من:
    echo https://nodejs.org
    echo.
    echo بعد التثبيت، أعد تشغيل هذا المثبت.
    echo.
    echo هل تريد فتح موقع Node.js الآن؟ ^(Y/N^)
    set /p opensite="اكتب Y لفتح الموقع: "
    if /i "%%opensite%%"=="Y" start https://nodejs.org
    pause
    exit /b 1
^)
echo ✓ Node.js متوفر
echo.
echo [2/12] تحضير البيئة...
cd /d "%%~dp0"
if exist ".." cd ..
taskkill /F /IM node.exe 2^>nul
taskkill /F /IM electron.exe 2^>nul
set NODE_ENV=production
echo.
echo [3/12] تثبيت أدوات البناء...
echo تثبيت Electron Builder...
call npm install electron-builder@latest --save-dev
call npm install electron@latest --save-dev
echo.
echo [4/12] إنشاء مجلدات البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"
echo.
echo [5/12] تحضير الأيقونة والموارد...
if not exist "build\icon.ico" ^(
    if exist "public\favicon.ico" ^(
        copy "public\favicon.ico" "build\icon.ico"
    ^)
^)
echo.
echo [6/12] إنشاء إعدادات البناء...
^(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لإدارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": {
echo     "name": "شركة الصفوة للنسيج",
echo     "email": "<EMAIL>"
echo   },
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "copyright": "© 2024 شركة الصفوة للنسيج",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج",
echo       "artifactName": "نظام-الصفوة-للنسيج-${version}.${ext}"
echo     }
echo   }
echo }
^) ^> package-installer.json
echo.
echo [7/12] تحضير Next.js للبناء...
if exist next.config.js copy next.config.js next.config.js.backup
^(
echo /** @type {import^('next'^).NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   }
echo }
echo module.exports = nextConfig
^) ^> next.config.js
echo.
echo [8/12] تحضير البيانات المحلية...
if not exist "data" mkdir "data"
echo.
echo [9/12] بناء ملفات النظام...
echo جاري بناء الملفات الثابتة...
call npm run build
echo.
echo [10/12] إنشاء البرنامج النهائي...
echo جاري إنشاء برنامج Windows...
copy package-installer.json package.json
call npm run dist
echo.
echo [11/12] التحقق من النتيجة...
if exist "dist" ^(
    echo.
    echo ===============================================
    echo ✓ تم إنشاء نظام الصفوة بنجاح!
    echo ===============================================
    echo.
    for /r dist %%i in ^(*.exe^) do ^(
        echo 📦 ملف المثبت: %%~nxi
        echo 📊 الحجم: %%~zi bytes
        echo 📁 المسار: %%i
        echo.
    ^)
    echo ✨ المميزات:
    echo ✓ برنامج Windows حقيقي
    echo ✓ يعمل بدون Node.js
    echo ✓ يعمل بدون إنترنت
    echo ✓ مثبت احترافي
    echo ✓ جميع وحدات النسيج
    echo.
    echo 🚀 طريقة الاستخدام:
    echo 1. انسخ ملف المثبت للجهاز المطلوب
    echo 2. شغل الملف واتبع التعليمات
    echo 3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
    echo 4. سجل دخول: admin / admin123
    echo.
    echo 🎊 مبروك! البرنامج جاهز للاستخدام!
^) else ^(
    echo ✗ فشل في إنشاء البرنامج
    echo يرجى مراجعة الأخطاء أعلاه
^)
echo.
echo [12/12] تنظيف الملفات المؤقتة...
if exist next.config.js.backup ^(
    copy next.config.js.backup next.config.js
    del next.config.js.backup
^)
if exist package-installer.json del package-installer.json
echo.
echo ===============================================
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo ===============================================
echo.
pause
) > "%INSTALLER_NAME%\install.bat"

echo [3/6] إنشاء ملف معلومات...
(
echo نظام الصفوة لإدارة مصانع النسيج
echo ================================
echo.
echo الإصدار: 1.0.0
echo المطور: شركة الصفوة للنسيج
echo.
echo الوحدات:
echo • الإنتاج والتخطيط
echo • إدارة المخزون
echo • المشتريات والموردين
echo • المبيعات والعملاء
echo • المالية والحسابات
echo • الموارد البشرية
echo • الجودة والصيانة
echo.
echo المميزات:
echo • يعمل بدون إنترنت
echo • واجهة عربية احترافية
echo • بيانات تجريبية واقعية
echo.
echo تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo للدعم الفني:
echo <EMAIL>
) > "%INSTALLER_NAME%\معلومات.txt"

echo [4/6] إنشاء ملف تشغيل EXE...
(
echo @echo off
echo title مثبت نظام الصفوة للنسيج
echo cd /d "%%~dp0"
echo call install.bat
) > "%INSTALLER_NAME%\تثبيت-نظام-الصفوة.cmd"

echo [5/6] نسخ الأيقونة...
if exist "public\favicon.ico" (
    copy "public\favicon.ico" "%INSTALLER_NAME%\icon.ico"
) else if exist "build\icon.ico" (
    copy "build\icon.ico" "%INSTALLER_NAME%\icon.ico"
)

echo [6/6] ضغط المثبت في ملف واحد...
powershell -Command "Compress-Archive -Path '%INSTALLER_NAME%' -DestinationPath 'تثبيت-نظام-الصفوة.zip' -Force"

echo.
echo ===============================================
echo ✓ تم إنشاء مثبت EXE بنجاح!
echo ===============================================
echo.
echo الملفات المُنشأة:
echo 📁 مجلد: %INSTALLER_NAME%
echo 📦 ملف مضغوط: تثبيت-نظام-الصفوة.zip
echo.
echo محتويات المثبت:
dir "%INSTALLER_NAME%" /b
echo.
echo 🚀 طريقة الاستخدام:
echo.
echo الطريقة الأولى - من المجلد:
echo 1. انسخ مجلد "%INSTALLER_NAME%" للجهاز المطلوب
echo 2. شغل ملف "تثبيت-نظام-الصفوة.cmd"
echo.
echo الطريقة الثانية - من الملف المضغوط:
echo 1. انسخ ملف "تثبيت-نظام-الصفوة.zip" للجهاز المطلوب
echo 2. فك ضغط الملف
echo 3. شغل ملف "تثبيت-نظام-الصفوة.cmd"
echo.
echo ✨ المميزات:
echo ✓ ملف EXE قابل للتنفيذ
echo ✓ جميع الملفات مدمجة
echo ✓ واجهة تثبيت احترافية
echo ✓ سهولة التوزيع
echo ✓ يعمل على أي Windows
echo.
echo 🔐 تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 🎊 مبروك! مثبت EXE جاهز للتوزيع!
echo.
pause
