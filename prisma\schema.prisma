// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// نموذج المستخدمين والمصادقة
model User {
  id            String    @id @default(cuid())
  name          String?
  username      String    @unique
  email         String?
  emailVerified DateTime?
  image         String?
  password      String?
  role          String    @default("USER")
  department    String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // علاقات المصادقة
  accounts Account[]
  sessions Session[]

  // علاقات النظام
  createdOrders     WorkOrder[]     @relation("CreatedBy")
  assignedOrders    WorkOrder[]     @relation("AssignedTo")
  qualityChecks     QualityCheck[]
  inventoryMoves    InventoryMove[]
  purchaseOrders    PurchaseOrder[] @relation("CreatedBy")
  salesOrders       SalesOrder[]    @relation("CreatedBy")

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// نماذج المخزون
model Material {
  id          String      @id @default(cuid())
  code        String      @unique
  nameAr      String
  nameEn      String?
  type        String
  unit        String
  minStock    Float       @default(0)
  maxStock    Float?
  currentStock Float      @default(0)
  unitCost    Float       @default(0)
  supplierId  String?
  isActive    Boolean     @default(true)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  supplier      Supplier?       @relation(fields: [supplierId], references: [id])
  inventoryMoves InventoryMove[]
  orderItems    WorkOrderItem[]
  purchaseItems PurchaseOrderItem[]

  @@map("materials")
}

model Product {
  id          String   @id @default(cuid())
  code        String   @unique
  nameAr      String
  nameEn      String?
  description String?
  category    String
  unit        String
  sellingPrice Float   @default(0)
  costPrice   Float    @default(0)
  currentStock Float   @default(0)
  minStock    Float    @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  workOrders    WorkOrder[]
  salesItems    SalesOrderItem[]
  inventoryMoves InventoryMove[]
  qualityChecks QualityCheck[]

  @@map("products")
}

// نماذج الإنتاج
model WorkOrder {
  id            String         @id @default(cuid())
  orderNumber   String         @unique
  productId     String
  quantity      Float
  status        String         @default("PENDING")
  priority      String         @default("MEDIUM")
  startDate     DateTime?
  endDate       DateTime?
  completedDate DateTime?
  createdById   String
  assignedToId  String?
  notes         String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  product     Product         @relation(fields: [productId], references: [id])
  createdBy   User            @relation("CreatedBy", fields: [createdById], references: [id])
  assignedTo  User?           @relation("AssignedTo", fields: [assignedToId], references: [id])
  items       WorkOrderItem[]
  stages      ProductionStage[]
  qualityChecks QualityCheck[]

  @@map("work_orders")
}

model WorkOrderItem {
  id          String @id @default(cuid())
  workOrderId String
  materialId  String
  quantity    Float
  unitCost    Float  @default(0)

  workOrder WorkOrder @relation(fields: [workOrderId], references: [id], onDelete: Cascade)
  material  Material  @relation(fields: [materialId], references: [id])

  @@map("work_order_items")
}

model ProductionStage {
  id          String            @id @default(cuid())
  workOrderId String
  stage       String
  status      String            @default("PENDING")
  startTime   DateTime?
  endTime     DateTime?
  notes       String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  workOrder WorkOrder @relation(fields: [workOrderId], references: [id], onDelete: Cascade)

  @@map("production_stages")
}

// نماذج الجودة
model QualityCheck {
  id          String        @id @default(cuid())
  workOrderId String?
  productId   String?
  checkType   String
  status      String        @default("PENDING")
  checkedById String
  checkDate   DateTime      @default(now())
  notes       String?
  defects     String?       // JSON string for defect details
  approved    Boolean       @default(false)

  workOrder   WorkOrder? @relation(fields: [workOrderId], references: [id])
  product     Product?   @relation(fields: [productId], references: [id])
  checkedBy   User       @relation(fields: [checkedById], references: [id])

  @@map("quality_checks")
}

// نماذج المشتريات
model Supplier {
  id          String   @id @default(cuid())
  code        String   @unique
  nameAr      String
  nameEn      String?
  contactPerson String?
  phone       String?
  email       String?
  address     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  materials     Material[]
  purchaseOrders PurchaseOrder[]

  @@map("suppliers")
}

model PurchaseOrder {
  id          String              @id @default(cuid())
  orderNumber String              @unique
  supplierId  String
  status      String              @default("PENDING")
  orderDate   DateTime            @default(now())
  expectedDate DateTime?
  receivedDate DateTime?
  totalAmount Float               @default(0)
  createdById String
  notes       String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  supplier   Supplier            @relation(fields: [supplierId], references: [id])
  createdBy  User                @relation("CreatedBy", fields: [createdById], references: [id])
  items      PurchaseOrderItem[]

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id              String @id @default(cuid())
  purchaseOrderId String
  materialId      String
  quantity        Float
  unitPrice       Float
  totalPrice      Float

  purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  material      Material      @relation(fields: [materialId], references: [id])

  @@map("purchase_order_items")
}

// نماذج المبيعات
model Customer {
  id          String   @id @default(cuid())
  code        String   @unique
  nameAr      String
  nameEn      String?
  contactPerson String?
  phone       String?
  email       String?
  address     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  salesOrders SalesOrder[]

  @@map("customers")
}

model SalesOrder {
  id          String           @id @default(cuid())
  orderNumber String           @unique
  customerId  String
  status      String           @default("PENDING")
  orderDate   DateTime         @default(now())
  deliveryDate DateTime?
  totalAmount Float            @default(0)
  createdById String
  notes       String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  customer   Customer         @relation(fields: [customerId], references: [id])
  createdBy  User             @relation("CreatedBy", fields: [createdById], references: [id])
  items      SalesOrderItem[]

  @@map("sales_orders")
}

model SalesOrderItem {
  id           String @id @default(cuid())
  salesOrderId String
  productId    String
  quantity     Float
  unitPrice    Float
  totalPrice   Float

  salesOrder SalesOrder @relation(fields: [salesOrderId], references: [id], onDelete: Cascade)
  product    Product    @relation(fields: [productId], references: [id])

  @@map("sales_order_items")
}

// نموذج حركات المخزون
model InventoryMove {
  id          String        @id @default(cuid())
  materialId  String?
  productId   String?
  moveType    String
  quantity    Float
  unitCost    Float         @default(0)
  reference   String?       // رقم مرجعي (أمر شراء، أمر إنتاج، إلخ)
  notes       String?
  createdById String
  createdAt   DateTime      @default(now())

  material  Material? @relation(fields: [materialId], references: [id])
  product   Product?  @relation(fields: [productId], references: [id])
  createdBy User      @relation(fields: [createdById], references: [id])

  @@map("inventory_moves")
}

// ملاحظة: تم تحويل جميع الـ enums إلى strings لتوافق SQLite
// القيم المسموحة:
// UserRole: "ADMIN", "MANAGER", "SUPERVISOR", "OPERATOR", "USER"
// MaterialType: "RAW_MATERIAL", "SEMI_FINISHED", "FINISHED", "CONSUMABLE", "SPARE_PARTS"
// WorkOrderStatus: "PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED", "ON_HOLD"
// Priority: "LOW", "MEDIUM", "HIGH", "URGENT"
// TextileStage: "SPINNING", "WEAVING", "DYEING", "FINISHING", "QUALITY_CHECK", "PACKAGING"
// StageStatus: "PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"
// QualityType: "INCOMING", "IN_PROCESS", "FINAL", "RANDOM"
// QualityStatus: "PENDING", "PASSED", "FAILED", "REWORK"
// PurchaseOrderStatus: "PENDING", "APPROVED", "SENT", "RECEIVED", "CANCELLED"
// SalesOrderStatus: "PENDING", "CONFIRMED", "IN_PRODUCTION", "READY", "SHIPPED", "DELIVERED", "CANCELLED"
// InventoryMoveType: "IN", "OUT", "TRANSFER", "ADJUSTMENT", "PRODUCTION", "SALE", "PURCHASE", "RETURN"
