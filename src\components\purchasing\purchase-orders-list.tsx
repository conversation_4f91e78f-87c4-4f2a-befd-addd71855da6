'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDate, formatCurrency } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, CheckCircle, Send, Truck } from 'lucide-react'

// بيانات تجريبية لأوامر الشراء
const mockPurchaseOrders = [
  {
    id: '1',
    orderNumber: 'PO-2024-001',
    supplier: 'شركة القطن المصري',
    supplierCode: 'SUP001',
    status: 'APPROVED',
    orderDate: new Date('2024-01-20'),
    expectedDate: new Date('2024-01-27'),
    receivedDate: null,
    totalAmount: 15500,
    itemsCount: 3,
    notes: 'طلب شراء قطن للإنتاج الشهري',
    createdBy: 'مدير المشتريات',
  },
  {
    id: '2',
    orderNumber: 'PO-2024-002',
    supplier: 'مصنع الألوان الحديث',
    supplierCode: 'SUP002',
    status: 'SENT',
    orderDate: new Date('2024-01-22'),
    expectedDate: new Date('2024-01-29'),
    receivedDate: null,
    totalAmount: 8900,
    itemsCount: 5,
    notes: 'أصباغ متنوعة للإنتاج',
    createdBy: 'مدير المشتريات',
  },
  {
    id: '3',
    orderNumber: 'PO-2024-003',
    supplier: 'شركة الخيوط المتقدمة',
    supplierCode: 'SUP003',
    status: 'RECEIVED',
    orderDate: new Date('2024-01-15'),
    expectedDate: new Date('2024-01-22'),
    receivedDate: new Date('2024-01-21'),
    totalAmount: 12300,
    itemsCount: 2,
    notes: 'خيوط عالية الجودة',
    createdBy: 'مدير المشتريات',
  },
  {
    id: '4',
    orderNumber: 'PO-2024-004',
    supplier: 'مؤسسة قطع الغيار الصناعية',
    supplierCode: 'SUP004',
    status: 'PENDING',
    orderDate: new Date('2024-01-25'),
    expectedDate: new Date('2024-02-01'),
    receivedDate: null,
    totalAmount: 5600,
    itemsCount: 8,
    notes: 'قطع غيار للصيانة الدورية',
    createdBy: 'مدير الصيانة',
  },
]

const statusLabels = {
  PENDING: 'في الانتظار',
  APPROVED: 'معتمد',
  SENT: 'مرسل',
  RECEIVED: 'مستلم',
  CANCELLED: 'ملغي',
}

export function PurchaseOrdersList() {
  const [purchaseOrders] = useState(mockPurchaseOrders)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'APPROVED':
        return 'bg-blue-100 text-blue-800'
      case 'SENT':
        return 'bg-purple-100 text-purple-800'
      case 'RECEIVED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-3 w-3" />
      case 'SENT':
        return <Send className="h-3 w-3" />
      case 'RECEIVED':
        return <Truck className="h-3 w-3" />
      default:
        return null
    }
  }

  const isOverdue = (expectedDate: Date, status: string) => {
    return status !== 'RECEIVED' && status !== 'CANCELLED' && new Date() > expectedDate
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم الأمر</TableHead>
            <TableHead>المورد</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>تاريخ الطلب</TableHead>
            <TableHead>التاريخ المتوقع</TableHead>
            <TableHead>تاريخ الاستلام</TableHead>
            <TableHead>عدد الأصناف</TableHead>
            <TableHead>إجمالي المبلغ</TableHead>
            <TableHead>المنشئ</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {purchaseOrders.map((order) => (
            <TableRow key={order.id} className={isOverdue(order.expectedDate, order.status) ? 'bg-red-50' : ''}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  {getStatusIcon(order.status)}
                  {order.orderNumber}
                  {isOverdue(order.expectedDate, order.status) && (
                    <span className="text-red-500 text-xs">(متأخر)</span>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{order.supplier}</div>
                  <div className="text-sm text-muted-foreground">
                    {order.supplierCode}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(order.status)}>
                  {statusLabels[order.status as keyof typeof statusLabels]}
                </Badge>
              </TableCell>
              <TableCell>
                {formatDate(order.orderDate)}
              </TableCell>
              <TableCell>
                <div className={isOverdue(order.expectedDate, order.status) ? 'text-red-600 font-medium' : ''}>
                  {formatDate(order.expectedDate)}
                </div>
              </TableCell>
              <TableCell>
                {order.receivedDate ? formatDate(order.receivedDate) : '-'}
              </TableCell>
              <TableCell>
                <div className="text-center">
                  {order.itemsCount} صنف
                </div>
              </TableCell>
              <TableCell>
                <div className="font-medium">
                  {formatCurrency(order.totalAmount)}
                </div>
              </TableCell>
              <TableCell>
                <div className="text-sm">{order.createdBy}</div>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Eye className="ml-2 h-4 w-4" />
                      عرض التفاصيل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </DropdownMenuItem>
                    {order.status === 'PENDING' && (
                      <DropdownMenuItem>
                        <CheckCircle className="ml-2 h-4 w-4" />
                        اعتماد
                      </DropdownMenuItem>
                    )}
                    {order.status === 'APPROVED' && (
                      <DropdownMenuItem>
                        <Send className="ml-2 h-4 w-4" />
                        إرسال للمورد
                      </DropdownMenuItem>
                    )}
                    {order.status === 'SENT' && (
                      <DropdownMenuItem>
                        <Truck className="ml-2 h-4 w-4" />
                        تأكيد الاستلام
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="ml-2 h-4 w-4" />
                      إلغاء
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
