'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Wallet,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  ChevronRight,
  ChevronDown
} from 'lucide-react'

interface Account {
  id: string
  code: string
  name: string
  type: string
  parentId?: string
  level: number
  balance: number
  debitBalance: number
  creditBalance: number
  isActive: boolean
  children?: Account[]
}

const mockAccounts: Account[] = [
  {
    id: '1',
    code: '1000',
    name: 'الأصول',
    type: 'ASSET',
    level: 1,
    balance: 500000,
    debitBalance: 500000,
    creditBalance: 0,
    isActive: true,
    children: [
      {
        id: '2',
        code: '1100',
        name: 'الأصول المتداولة',
        type: 'ASSET',
        parentId: '1',
        level: 2,
        balance: 300000,
        debitBalance: 300000,
        creditBalance: 0,
        isActive: true,
        children: [
          {
            id: '3',
            code: '1110',
            name: 'النقدية والبنوك',
            type: 'ASSET',
            parentId: '2',
            level: 3,
            balance: 150000,
            debitBalance: 150000,
            creditBalance: 0,
            isActive: true
          },
          {
            id: '4',
            code: '1120',
            name: 'المخزون',
            type: 'ASSET',
            parentId: '2',
            level: 3,
            balance: 100000,
            debitBalance: 100000,
            creditBalance: 0,
            isActive: true
          },
          {
            id: '5',
            code: '1130',
            name: 'العملاء',
            type: 'ASSET',
            parentId: '2',
            level: 3,
            balance: 50000,
            debitBalance: 50000,
            creditBalance: 0,
            isActive: true
          }
        ]
      },
      {
        id: '6',
        code: '1200',
        name: 'الأصول الثابتة',
        type: 'ASSET',
        parentId: '1',
        level: 2,
        balance: 200000,
        debitBalance: 200000,
        creditBalance: 0,
        isActive: true,
        children: [
          {
            id: '7',
            code: '1210',
            name: 'الماكينات والمعدات',
            type: 'ASSET',
            parentId: '6',
            level: 3,
            balance: 150000,
            debitBalance: 150000,
            creditBalance: 0,
            isActive: true
          },
          {
            id: '8',
            code: '1220',
            name: 'المباني',
            type: 'ASSET',
            parentId: '6',
            level: 3,
            balance: 50000,
            debitBalance: 50000,
            creditBalance: 0,
            isActive: true
          }
        ]
      }
    ]
  },
  {
    id: '9',
    code: '2000',
    name: 'الخصوم',
    type: 'LIABILITY',
    level: 1,
    balance: 200000,
    debitBalance: 0,
    creditBalance: 200000,
    isActive: true,
    children: [
      {
        id: '10',
        code: '2100',
        name: 'الخصوم المتداولة',
        type: 'LIABILITY',
        parentId: '9',
        level: 2,
        balance: 120000,
        debitBalance: 0,
        creditBalance: 120000,
        isActive: true,
        children: [
          {
            id: '11',
            code: '2110',
            name: 'الموردين',
            type: 'LIABILITY',
            parentId: '10',
            level: 3,
            balance: 80000,
            debitBalance: 0,
            creditBalance: 80000,
            isActive: true
          },
          {
            id: '12',
            code: '2120',
            name: 'المصروفات المستحقة',
            type: 'LIABILITY',
            parentId: '10',
            level: 3,
            balance: 40000,
            debitBalance: 0,
            creditBalance: 40000,
            isActive: true
          }
        ]
      },
      {
        id: '13',
        code: '2200',
        name: 'الخصوم طويلة الأجل',
        type: 'LIABILITY',
        parentId: '9',
        level: 2,
        balance: 80000,
        debitBalance: 0,
        creditBalance: 80000,
        isActive: true,
        children: [
          {
            id: '14',
            code: '2210',
            name: 'القروض طويلة الأجل',
            type: 'LIABILITY',
            parentId: '13',
            level: 3,
            balance: 80000,
            debitBalance: 0,
            creditBalance: 80000,
            isActive: true
          }
        ]
      }
    ]
  },
  {
    id: '15',
    code: '3000',
    name: 'حقوق الملكية',
    type: 'EQUITY',
    level: 1,
    balance: 300000,
    debitBalance: 0,
    creditBalance: 300000,
    isActive: true,
    children: [
      {
        id: '16',
        code: '3100',
        name: 'رأس المال',
        type: 'EQUITY',
        parentId: '15',
        level: 2,
        balance: 250000,
        debitBalance: 0,
        creditBalance: 250000,
        isActive: true
      },
      {
        id: '17',
        code: '3200',
        name: 'الأرباح المحتجزة',
        type: 'EQUITY',
        parentId: '15',
        level: 2,
        balance: 50000,
        debitBalance: 0,
        creditBalance: 50000,
        isActive: true
      }
    ]
  }
]

const getAccountTypeColor = (type: string) => {
  switch (type) {
    case 'ASSET': return 'bg-blue-500'
    case 'LIABILITY': return 'bg-red-500'
    case 'EQUITY': return 'bg-green-500'
    case 'REVENUE': return 'bg-purple-500'
    case 'EXPENSE': return 'bg-orange-500'
    default: return 'bg-gray-500'
  }
}

const getAccountTypeText = (type: string) => {
  switch (type) {
    case 'ASSET': return 'أصول'
    case 'LIABILITY': return 'خصوم'
    case 'EQUITY': return 'حقوق ملكية'
    case 'REVENUE': return 'إيرادات'
    case 'EXPENSE': return 'مصروفات'
    default: return 'غير محدد'
  }
}

interface AccountRowProps {
  account: Account
  expandedAccounts: Set<string>
  onToggleExpand: (accountId: string) => void
}

function AccountRow({ account, expandedAccounts, onToggleExpand }: AccountRowProps) {
  const hasChildren = account.children && account.children.length > 0
  const isExpanded = expandedAccounts.has(account.id)
  const indentLevel = (account.level - 1) * 20

  return (
    <>
      <div 
        className="border rounded-lg p-4 hover:bg-gray-50"
        style={{ marginRight: `${indentLevel}px` }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            {hasChildren && (
              <button
                onClick={() => onToggleExpand(account.id)}
                className="p-1 hover:bg-gray-200 rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </button>
            )}
            {!hasChildren && <div className="w-6" />}
            
            <Badge className={getAccountTypeColor(account.type)}>
              {getAccountTypeText(account.type)}
            </Badge>
            
            <div>
              <h3 className="font-semibold">{account.name}</h3>
              <p className="text-sm text-gray-600">كود: {account.code}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="text-left">
              <p className="text-sm text-gray-600">الرصيد</p>
              <p className={`font-bold ${account.balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {account.balance.toLocaleString()} ر.س
              </p>
            </div>
            
            <div className="flex space-x-2 space-x-reverse">
              <Button size="sm" variant="outline">
                <Eye className="h-4 w-4 ml-1" />
                عرض
              </Button>
              <Button size="sm" variant="outline">
                <Edit className="h-4 w-4 ml-1" />
                تعديل
              </Button>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
          <div>
            <p className="text-sm text-gray-600">رصيد مدين</p>
            <p className="font-medium">{account.debitBalance.toLocaleString()} ر.س</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">رصيد دائن</p>
            <p className="font-medium">{account.creditBalance.toLocaleString()} ر.س</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">الحالة</p>
            <Badge variant={account.isActive ? "default" : "secondary"}>
              {account.isActive ? 'نشط' : 'غير نشط'}
            </Badge>
          </div>
        </div>
      </div>
      
      {hasChildren && isExpanded && account.children?.map((child) => (
        <AccountRow
          key={child.id}
          account={child}
          expandedAccounts={expandedAccounts}
          onToggleExpand={onToggleExpand}
        />
      ))}
    </>
  )
}

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>(mockAccounts)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [expandedAccounts, setExpandedAccounts] = useState<Set<string>>(new Set(['1', '9', '15']))

  const handleToggleExpand = (accountId: string) => {
    const newExpanded = new Set(expandedAccounts)
    if (newExpanded.has(accountId)) {
      newExpanded.delete(accountId)
    } else {
      newExpanded.add(accountId)
    }
    setExpandedAccounts(newExpanded)
  }

  // Flatten accounts for filtering
  const flattenAccounts = (accounts: Account[]): Account[] => {
    const result: Account[] = []
    accounts.forEach(account => {
      result.push(account)
      if (account.children) {
        result.push(...flattenAccounts(account.children))
      }
    })
    return result
  }

  const allAccounts = flattenAccounts(accounts)
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         account.code.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || account.type === selectedType
    return matchesSearch && matchesType
  })

  const totalAssets = allAccounts
    .filter(a => a.type === 'ASSET')
    .reduce((acc, a) => acc + a.balance, 0)

  const totalLiabilities = allAccounts
    .filter(a => a.type === 'LIABILITY')
    .reduce((acc, a) => acc + a.balance, 0)

  const totalEquity = allAccounts
    .filter(a => a.type === 'EQUITY')
    .reduce((acc, a) => acc + a.balance, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">شجرة الحسابات</h1>
          <p className="text-gray-600">إدارة الحسابات المحاسبية والأرصدة</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            حساب جديد
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الحسابات</p>
                <p className="text-2xl font-bold text-blue-600">{allAccounts.length}</p>
              </div>
              <Wallet className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الأصول</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalAssets.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الخصوم</p>
                <p className="text-2xl font-bold text-red-600">
                  {totalLiabilities.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">حقوق الملكية</p>
                <p className="text-2xl font-bold text-purple-600">
                  {totalEquity.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحسابات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="ASSET">أصول</option>
                <option value="LIABILITY">خصوم</option>
                <option value="EQUITY">حقوق ملكية</option>
                <option value="REVENUE">إيرادات</option>
                <option value="EXPENSE">مصروفات</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Accounts Tree */}
      <Card>
        <CardHeader>
          <CardTitle>شجرة الحسابات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredAccounts.map((account) => (
              <AccountRow
                key={account.id}
                account={account}
                expandedAccounts={expandedAccounts}
                onToggleExpand={handleToggleExpand}
              />
            ))}

            {filteredAccounts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد حسابات تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
