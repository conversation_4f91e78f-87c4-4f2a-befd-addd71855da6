'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Wrench,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  Settings,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Plus
} from 'lucide-react'

interface MaintenanceRecord {
  id: string
  equipmentId: string
  equipmentName: string
  location: string
  maintenanceType: string
  priority: string
  status: string
  scheduledDate: Date
  completedDate?: Date
  technician: string
  description: string
  cost: number
  downtime: number
  notes?: string
  spareParts: string[]
}

const mockMaintenance: MaintenanceRecord[] = [
  {
    id: '1',
    equipmentId: 'EQ001',
    equipmentName: 'نول رقم 1',
    location: 'قسم النسيج - خط أ',
    maintenanceType: 'PREVENTIVE',
    priority: 'MEDIUM',
    status: 'COMPLETED',
    scheduledDate: new Date('2024-01-15'),
    completedDate: new Date('2024-01-15'),
    technician: 'محمد أحمد',
    description: 'صيانة دورية شهرية',
    cost: 500,
    downtime: 2,
    notes: 'تم تغيير الزيوت والفلاتر',
    spareParts: ['فلتر هواء', 'زيت محرك']
  },
  {
    id: '2',
    equipmentId: 'EQ002',
    equipmentName: 'ماكينة صباغة رقم 2',
    location: 'قسم الصباغة',
    maintenanceType: 'CORRECTIVE',
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    scheduledDate: new Date('2024-01-16'),
    technician: 'أحمد علي',
    description: 'إصلاح تسريب في نظام التبريد',
    cost: 1200,
    downtime: 8,
    notes: 'يحتاج قطع غيار من المورد',
    spareParts: ['مضخة مياه', 'خراطيم']
  },
  {
    id: '3',
    equipmentId: 'EQ003',
    equipmentName: 'ماكينة غزل رقم 3',
    location: 'قسم الغزل',
    maintenanceType: 'EMERGENCY',
    priority: 'URGENT',
    status: 'PENDING',
    scheduledDate: new Date('2024-01-17'),
    technician: 'فاطمة محمود',
    description: 'توقف مفاجئ في المحرك الرئيسي',
    cost: 2000,
    downtime: 0,
    notes: 'طوارئ - يحتاج تدخل فوري',
    spareParts: ['محرك كهربائي', 'كابلات']
  },
  {
    id: '4',
    equipmentId: 'EQ004',
    equipmentName: 'نظام التكييف المركزي',
    location: 'المبنى الرئيسي',
    maintenanceType: 'PREVENTIVE',
    priority: 'LOW',
    status: 'SCHEDULED',
    scheduledDate: new Date('2024-01-20'),
    technician: 'خالد عبدالله',
    description: 'تنظيف وصيانة دورية',
    cost: 300,
    downtime: 1,
    spareParts: ['فلاتر هواء']
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'bg-blue-500'
    case 'PENDING': return 'bg-yellow-500'
    case 'IN_PROGRESS': return 'bg-orange-500'
    case 'COMPLETED': return 'bg-green-500'
    case 'CANCELLED': return 'bg-gray-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'SCHEDULED': return 'مجدولة'
    case 'PENDING': return 'في الانتظار'
    case 'IN_PROGRESS': return 'قيد التنفيذ'
    case 'COMPLETED': return 'مكتملة'
    case 'CANCELLED': return 'ملغية'
    default: return 'غير محدد'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'bg-red-500'
    case 'HIGH': return 'bg-orange-500'
    case 'MEDIUM': return 'bg-yellow-500'
    case 'LOW': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'URGENT': return 'عاجل'
    case 'HIGH': return 'عالية'
    case 'MEDIUM': return 'متوسطة'
    case 'LOW': return 'منخفضة'
    default: return 'غير محدد'
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'PREVENTIVE': return 'bg-blue-500'
    case 'CORRECTIVE': return 'bg-orange-500'
    case 'EMERGENCY': return 'bg-red-500'
    case 'PREDICTIVE': return 'bg-purple-500'
    default: return 'bg-gray-500'
  }
}

const getTypeText = (type: string) => {
  switch (type) {
    case 'PREVENTIVE': return 'وقائية'
    case 'CORRECTIVE': return 'إصلاحية'
    case 'EMERGENCY': return 'طوارئ'
    case 'PREDICTIVE': return 'تنبؤية'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED': return <CheckCircle className="h-4 w-4" />
    case 'IN_PROGRESS': return <Settings className="h-4 w-4" />
    case 'PENDING': return <Clock className="h-4 w-4" />
    case 'SCHEDULED': return <Calendar className="h-4 w-4" />
    default: return <AlertTriangle className="h-4 w-4" />
  }
}

export default function MaintenancePage() {
  const [maintenance, setMaintenance] = useState<MaintenanceRecord[]>(mockMaintenance)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedPriority, setSelectedPriority] = useState<string>('all')

  const filteredMaintenance = maintenance.filter(record => {
    const matchesSearch = record.equipmentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.equipmentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.technician.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || record.status === selectedStatus
    const matchesType = selectedType === 'all' || record.maintenanceType === selectedType
    const matchesPriority = selectedPriority === 'all' || record.priority === selectedPriority
    return matchesSearch && matchesStatus && matchesType && matchesPriority
  })

  const totalCost = maintenance.reduce((acc, r) => acc + r.cost, 0)
  const totalDowntime = maintenance.reduce((acc, r) => acc + r.downtime, 0)
  const completedCount = maintenance.filter(r => r.status === 'COMPLETED').length
  const urgentCount = maintenance.filter(r => r.priority === 'URGENT' && r.status !== 'COMPLETED').length

  const handleStartMaintenance = (recordId: string) => {
    setMaintenance(maintenance.map(record => 
      record.id === recordId ? { ...record, status: 'IN_PROGRESS' } : record
    ))
  }

  const handleCompleteMaintenance = (recordId: string) => {
    setMaintenance(maintenance.map(record => 
      record.id === recordId 
        ? { 
            ...record, 
            status: 'COMPLETED',
            completedDate: new Date()
          }
        : record
    ))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الصيانة</h1>
          <p className="text-gray-600">إدارة صيانة الماكينات والمعدات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير التقرير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            صيانة جديدة
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التكلفة</p>
                <p className="text-2xl font-bold text-blue-600">
                  {totalCost.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">ساعات التوقف</p>
                <p className="text-2xl font-bold text-red-600">{totalDowntime}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مكتملة</p>
                <p className="text-2xl font-bold text-green-600">{completedCount}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">عاجلة</p>
                <p className="text-2xl font-bold text-red-600">{urgentCount}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الصيانة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="SCHEDULED">مجدولة</option>
                <option value="PENDING">في الانتظار</option>
                <option value="IN_PROGRESS">قيد التنفيذ</option>
                <option value="COMPLETED">مكتملة</option>
                <option value="CANCELLED">ملغية</option>
              </select>
              
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="PREVENTIVE">وقائية</option>
                <option value="CORRECTIVE">إصلاحية</option>
                <option value="EMERGENCY">طوارئ</option>
                <option value="PREDICTIVE">تنبؤية</option>
              </select>

              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأولويات</option>
                <option value="URGENT">عاجل</option>
                <option value="HIGH">عالية</option>
                <option value="MEDIUM">متوسطة</option>
                <option value="LOW">منخفضة</option>
              </select>

              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Records */}
      <Card>
        <CardHeader>
          <CardTitle>سجل الصيانة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredMaintenance.map((record) => (
              <div key={record.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(record.status)}>
                      {getStatusIcon(record.status)}
                      <span className="mr-1">{getStatusText(record.status)}</span>
                    </Badge>
                    <Badge className={getTypeColor(record.maintenanceType)}>
                      {getTypeText(record.maintenanceType)}
                    </Badge>
                    <Badge className={getPriorityColor(record.priority)}>
                      {getPriorityText(record.priority)}
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{record.equipmentName}</h3>
                      <p className="text-sm text-gray-600">
                        {record.equipmentId} - {record.location}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {record.status === 'PENDING' && (
                      <Button 
                        size="sm"
                        onClick={() => handleStartMaintenance(record.id)}
                        className="bg-orange-600 hover:bg-orange-700"
                      >
                        <Settings className="h-4 w-4 ml-1" />
                        بدء الصيانة
                      </Button>
                    )}
                    {record.status === 'IN_PROGRESS' && (
                      <Button 
                        size="sm"
                        onClick={() => handleCompleteMaintenance(record.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 ml-1" />
                        إكمال
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">الفني المسؤول</p>
                    <p className="font-medium">{record.technician}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">التاريخ المجدول</p>
                    <p className="font-medium">{record.scheduledDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">التكلفة</p>
                    <p className="font-medium">{record.cost.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">ساعات التوقف</p>
                    <p className={`font-medium ${record.downtime > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {record.downtime} ساعة
                    </p>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-1">الوصف</p>
                  <p className="font-medium">{record.description}</p>
                </div>

                {record.spareParts.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2">قطع الغيار المطلوبة:</p>
                    <div className="flex flex-wrap gap-2">
                      {record.spareParts.map((part, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {part}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {record.completedDate && (
                  <div className="mb-3 p-2 bg-green-100 rounded text-sm">
                    <strong>تاريخ الإكمال:</strong> {record.completedDate.toLocaleDateString('ar-SA')}
                  </div>
                )}

                {record.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {record.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredMaintenance.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد سجلات صيانة تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
