'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { AddPurchaseOrderDialog } from './add-purchase-order-dialog'
import { Plus, Search, Filter, Download, ShoppingCart, FileText } from 'lucide-react'

export function PurchaseOrdersHeader() {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterSupplier, setFilterSupplier] = useState('all')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <ShoppingCart className="h-6 w-6" />
            أوامر الشراء
          </h1>
          <p className="text-muted-foreground">
            إدارة ومتابعة أوامر الشراء والتوريد
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 ml-2" />
            تقرير المشتريات
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 ml-2" />
            تصدير
          </Button>
          
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="h-4 w-4 ml-2" />
            أمر شراء جديد
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="البحث في أوامر الشراء..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pr-10"
          />
        </div>

        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-48">
            <Filter className="h-4 w-4 ml-2" />
            <SelectValue placeholder="تصفية حسب الحالة" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الحالات</SelectItem>
            <SelectItem value="PENDING">في الانتظار</SelectItem>
            <SelectItem value="APPROVED">معتمد</SelectItem>
            <SelectItem value="SENT">مرسل</SelectItem>
            <SelectItem value="RECEIVED">مستلم</SelectItem>
            <SelectItem value="CANCELLED">ملغي</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSupplier} onValueChange={setFilterSupplier}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="تصفية حسب المورد" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">جميع الموردين</SelectItem>
            <SelectItem value="SUP001">شركة القطن المصري</SelectItem>
            <SelectItem value="SUP002">مصنع الألوان الحديث</SelectItem>
            <SelectItem value="SUP003">شركة الخيوط المتقدمة</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <AddPurchaseOrderDialog 
        open={isAddDialogOpen} 
        onOpenChange={setIsAddDialogOpen}
      />
    </div>
  )
}
