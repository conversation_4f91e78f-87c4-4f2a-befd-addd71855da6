@echo off
chcp 65001 >nul
echo.
echo 📦 إنشاء حزمة نظام الصفوة للتوزيع
echo ===================================
echo.

echo [1/8] إعداد مجلد الحزمة...
if exist "SafaTextile-Offline" rmdir /s /q "SafaTextile-Offline"
mkdir "SafaTextile-Offline"
mkdir "SafaTextile-Offline\app"
mkdir "SafaTextile-Offline\data"
mkdir "SafaTextile-Offline\docs"
mkdir "SafaTextile-Offline\tools"

echo [2/8] بناء النظام للإنتاج...
copy "next.config.offline.js" "next.config.js" >nul
cmd /c "npm run build"

echo [3/8] نسخ ملفات التطبيق...
xcopy "src" "SafaTextile-Offline\app\src" /e /i /q >nul
xcopy "public" "SafaTextile-Offline\app\public" /e /i /q >nul
xcopy "prisma" "SafaTextile-Offline\app\prisma" /e /i /q >nul
xcopy ".next" "SafaTextile-Offline\app\.next" /e /i /q >nul
xcopy "node_modules" "SafaTextile-Offline\app\node_modules" /e /i /q >nul

echo [4/8] نسخ ملفات الإعداد...
copy "package.json" "SafaTextile-Offline\app\" >nul
copy "next.config.offline.js" "SafaTextile-Offline\app\next.config.js" >nul
copy ".env.offline" "SafaTextile-Offline\app\.env" >nul

echo [5/8] نسخ أدوات Windows...
copy "start-offline.bat" "SafaTextile-Offline\" >nul
copy "offline-setup.bat" "SafaTextile-Offline\tools\" >nul
copy "update-passwords.bat" "SafaTextile-Offline\tools\" >nul
copy "login-help.bat" "SafaTextile-Offline\tools\" >nul

echo [6/8] نسخ الوثائق...
copy "OFFLINE_GUIDE.md" "SafaTextile-Offline\docs\" >nul
copy "README_WINDOWS.md" "SafaTextile-Offline\docs\" >nul
copy "EASY_INSTALL.md" "SafaTextile-Offline\docs\" >nul

echo [7/8] إنشاء ملف التشغيل الرئيسي...
echo @echo off > "SafaTextile-Offline\تشغيل النظام.bat"
echo chcp 65001 ^>nul >> "SafaTextile-Offline\تشغيل النظام.bat"
echo echo. >> "SafaTextile-Offline\تشغيل النظام.bat"
echo echo 🏢 نظام الصفوة للنسيج - الإصدار المحلي >> "SafaTextile-Offline\تشغيل النظام.bat"
echo echo ======================================= >> "SafaTextile-Offline\تشغيل النظام.bat"
echo echo. >> "SafaTextile-Offline\تشغيل النظام.bat"
echo cd app >> "SafaTextile-Offline\تشغيل النظام.bat"
echo npm start >> "SafaTextile-Offline\تشغيل النظام.bat"

echo [8/8] إنشاء ملف README...
echo # نظام الصفوة للنسيج - الإصدار المحلي > "SafaTextile-Offline\README.txt"
echo. >> "SafaTextile-Offline\README.txt"
echo ## التشغيل السريع: >> "SafaTextile-Offline\README.txt"
echo 1. انقر مرتين على "تشغيل النظام.bat" >> "SafaTextile-Offline\README.txt"
echo 2. افتح المتصفح على: http://localhost:3000 >> "SafaTextile-Offline\README.txt"
echo 3. سجل الدخول بـ: <EMAIL> / admin123 >> "SafaTextile-Offline\README.txt"
echo. >> "SafaTextile-Offline\README.txt"
echo ## المميزات: >> "SafaTextile-Offline\README.txt"
echo - يعمل بدون إنترنت >> "SafaTextile-Offline\README.txt"
echo - قاعدة بيانات محلية >> "SafaTextile-Offline\README.txt"
echo - أمان عالي >> "SafaTextile-Offline\README.txt"
echo - سرعة فائقة >> "SafaTextile-Offline\README.txt"
echo. >> "SafaTextile-Offline\README.txt"
echo ## للمساعدة: >> "SafaTextile-Offline\README.txt"
echo راجع ملفات docs/ للحصول على أدلة مفصلة >> "SafaTextile-Offline\README.txt"

echo.
echo ===================================
echo ✅ تم إنشاء الحزمة بنجاح!
echo ===================================
echo.
echo 📁 المجلد: SafaTextile-Offline
echo 📊 الحجم: ~500 MB
echo 🎯 جاهز للتوزيع
echo.
echo 📋 محتويات الحزمة:
echo - تشغيل النظام.bat (ملف التشغيل الرئيسي)
echo - app/ (ملفات التطبيق)
echo - data/ (قاعدة البيانات)
echo - docs/ (الوثائق)
echo - tools/ (أدوات مساعدة)
echo - README.txt (دليل سريع)
echo.
echo 🚀 لاختبار الحزمة:
echo 1. ادخل مجلد SafaTextile-Offline
echo 2. انقر مرتين على "تشغيل النظام.bat"
echo.
pause
