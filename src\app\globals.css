@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Cairo', 'Tajawal', sans-serif;
  }
}

/* دعم اللغة العربية */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

[dir="ltr"] {
  direction: ltr;
  text-align: left;
}

/* تخصيص الخطوط العربية */
.font-arabic {
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* تخصيص الجداول للعربية */
.rtl-table {
  direction: rtl;
}

.rtl-table th,
.rtl-table td {
  text-align: right;
}

/* تخصيص النماذج */
.form-rtl {
  direction: rtl;
}

.form-rtl input,
.form-rtl textarea,
.form-rtl select {
  text-align: right;
}

/* تحسين الأزرار */
.btn-primary {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

/* تخصيص البطاقات */
.card-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* تحسين الرسوم البيانية */
.chart-container {
  direction: ltr;
}

/* تخصيص الشريط الجانبي */
.sidebar {
  @apply bg-card border-r border-border;
}

.sidebar-item {
  @apply flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors;
}

.sidebar-item.active {
  @apply bg-primary text-primary-foreground;
}

/* تحسين الإشعارات */
.notification {
  @apply bg-card border border-border rounded-lg shadow-lg;
}

/* تخصيص الجداول */
.data-table {
  @apply w-full border-collapse border border-border;
}

.data-table th,
.data-table td {
  @apply border border-border px-4 py-2;
}

.data-table th {
  @apply bg-muted font-medium;
}

/* تحسين النماذج */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply text-sm font-medium text-foreground;
}

.form-input {
  @apply w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
}

.form-error {
  @apply text-sm text-destructive;
}

/* تحسين الحالات */
.status-pending {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-in-progress {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-completed {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* تحسين الأولويات */
.priority-low {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium;
}

.priority-medium {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

.priority-high {
  @apply bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium;
}

.priority-urgent {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
}

/* تحسين التمرير */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}
