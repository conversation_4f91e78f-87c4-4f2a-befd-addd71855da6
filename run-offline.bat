@echo off
echo.
echo Safa Textile ERP - Starting Offline Mode
echo =========================================
echo.

REM Kill any existing Node processes
taskkill /F /IM node.exe 2>nul

REM Wait a moment
timeout /t 2 /nobreak >nul

echo Checking system...
if not exist ".env" (
    echo Setting up offline configuration...
    echo DATABASE_URL="file:./data/database.db" > .env
    echo NEXTAUTH_URL="http://localhost:3000" >> .env
    echo NEXTAUTH_SECRET="safa-textile-offline-2024" >> .env
    echo OFFLINE_MODE="true" >> .env
)

if not exist "data" mkdir data

echo.
echo Starting Safa Textile ERP System...
echo ====================================
echo.
echo Mode: Offline (No Internet Required)
echo Database: SQLite Local File
echo URL: http://localhost:3000
echo.
echo Login Accounts:
echo - Admin: <EMAIL> / admin123
echo - Supervisor: <EMAIL> / super123
echo - Operator: <EMAIL> / oper123
echo.
echo Press Ctrl+C to stop the system
echo ====================================
echo.

REM Try to start on port 3000, if busy try 3001
cmd /c "npm start" 2>nul
if %errorlevel% neq 0 (
    echo Port 3000 is busy, trying port 3001...
    cmd /c "npm start -- -p 3001"
)
