@echo off
title تثبيت نظام الصفوة للنسيج - ملف واحد شامل
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           تثبيت نظام الصفوة للنسيج - ملف واحد شامل
echo                              🖥️ برنامج Windows حقيقي بنقرة واحدة 🖥️
echo.
echo ===============================================================================
echo.

echo مرحباً بك في مثبت نظام الصفوة لإدارة مصانع النسيج!
echo.
echo هذا المثبت الواحد سيقوم بـ:
echo ✓ إنشاء برنامج Windows حقيقي
echo ✓ يعمل مثل أي برنامج عادي (Office, Photoshop, إلخ)
echo ✓ لا يحتاج Node.js أو أي متطلبات
echo ✓ لا يحتاج إنترنت تماماً
echo ✓ مثبت احترافي مع اختصارات
echo ✓ جميع وحدات النسيج مدمجة
echo.
echo الوحدات المتاحة:
echo • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo • المالية: الحسابات، الفواتير، التقارير المالية
echo • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo هل تريد المتابعة؟ (Y/N)
set /p confirm="اكتب Y للمتابعة أو N للإلغاء: "

if /i "%confirm%" neq "Y" (
    echo تم إلغاء التثبيت.
    goto end
)

echo.
echo ===============================================
echo بدء تثبيت نظام الصفوة للنسيج
echo ===============================================
echo.

echo [1/12] تحضير البيئة...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM electron.exe 2>nul
set NODE_ENV=production

echo [2/12] تثبيت أدوات البناء...
echo تثبيت Electron Builder...
call npm install electron-builder@latest --save-dev
call npm install electron@latest --save-dev

echo [3/12] إنشاء مجلدات البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"

echo [4/12] تحضير الأيقونة...
if not exist "build\icon.ico" (
    if exist "public\favicon.ico" (
        copy "public\favicon.ico" "build\icon.ico"
    )
)

echo [5/12] إنشاء إعدادات البناء...
(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لإدارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": {
echo     "name": "شركة الصفوة للنسيج",
echo     "email": "<EMAIL>",
echo     "url": "https://safa-textile.com"
echo   },
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "copyright": "© 2024 شركة الصفوة للنسيج - جميع الحقوق محفوظة",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "extraFiles": [
echo       {
echo         "from": "data",
echo         "to": "data"
echo       }
echo     ],
echo     "win": {
echo       "target": [
echo         {
echo           "target": "nsis",
echo           "arch": ["x64"]
echo         }
echo       ],
echo       "icon": "build/icon.ico",
echo       "requestedExecutionLevel": "asInvoker",
echo       "publisherName": "شركة الصفوة للنسيج"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "allowElevation": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج",
echo       "installerIcon": "build/icon.ico",
echo       "uninstallerIcon": "build/icon.ico",
echo       "installerHeaderIcon": "build/icon.ico",
echo       "displayLanguageSelector": false,
echo       "language": "1025",
echo       "artifactName": "نظام-الصفوة-للنسيج-${version}.${ext}"
echo     }
echo   }
echo }
) > package-installer.json

echo [6/12] تحضير Next.js للبناء الثابت...
if exist next.config.js copy next.config.js next.config.js.backup
(
echo /** @type {import('next').NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   },
echo   experimental: {
echo     serverComponentsExternalPackages: ['@prisma/client', 'bcryptjs']
echo   }
echo }
echo module.exports = nextConfig
) > next.config.js

echo [7/12] تحضير البيانات المحلية...
if not exist "data" mkdir "data"

echo [8/12] بناء ملفات Next.js...
echo جاري بناء الملفات الثابتة...
call npm run build

echo [9/12] التحقق من الملفات المبنية...
if not exist "out\index.html" (
    echo ✗ فشل في بناء الملفات الثابتة
    goto error
)
echo ✓ تم بناء الملفات الثابتة بنجاح

echo [10/12] بناء البرنامج النهائي...
echo جاري إنشاء برنامج Windows...
copy package-installer.json package.json
call npm run dist

echo [11/12] التحقق من النتيجة النهائية...
if exist "dist" (
    echo.
    echo ===============================================
    echo ✓ تم إنشاء برنامج نظام الصفوة بنجاح!
    echo ===============================================
    echo.
    echo الملف المُنشأ:
    for /r dist %%i in (*.exe) do (
        echo 📦 اسم الملف: %%~nxi
        echo 📊 الحجم: %%~zi bytes
        echo 📁 المسار الكامل: %%i
        echo.
        echo ✨ مميزات البرنامج:
        echo ✓ برنامج Windows حقيقي
        echo ✓ يعمل بدون Node.js أو أي متطلبات
        echo ✓ يعمل بدون إنترنت تماماً
        echo ✓ مثبت احترافي مع اختصارات تلقائية
        echo ✓ إلغاء تثبيت آمن من لوحة التحكم
        echo ✓ جميع وحدات النسيج مدمجة
        echo ✓ واجهة عربية احترافية
        echo ✓ شعار الصفوة في جميع الصفحات
        echo ✓ بيانات تجريبية واقعية
        echo.
        echo 🚀 طريقة الاستخدام:
        echo 1. انسخ ملف "%%~nxi" للجهاز المطلوب
        echo 2. شغل الملف واتبع تعليمات التثبيت
        echo 3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
        echo 4. أو انقر على الاختصار في سطح المكتب
        echo 5. سجل دخول: admin / admin123
        echo.
        echo 🎊 مبروك! البرنامج جاهز للتوزيع والاستخدام!
    )
) else (
    echo ✗ فشل في إنشاء البرنامج
    goto error
)

echo [12/12] تنظيف الملفات المؤقتة...
if exist next.config.js.backup (
    copy next.config.js.backup next.config.js
    del next.config.js.backup
)
if exist package-installer.json del package-installer.json

echo.
echo ===============================================
echo اكتمل تثبيت نظام الصفوة للنسيج!
echo ===============================================
echo.
echo يمكنك الآن:
echo • توزيع ملف المثبت على أي جهاز Windows
echo • تثبيت البرنامج مثل أي برنامج عادي
echo • تشغيل البرنامج من قائمة ابدأ أو سطح المكتب
echo • استخدام جميع وحدات النسيج بدون إنترنت
echo • إدارة مصنع النسيج بشكل احترافي
echo.
echo البرنامج يعمل الآن مثل أي برنامج Windows حقيقي!
echo.
goto end

:error
echo.
echo ===============================================
echo حدث خطأ أثناء التثبيت!
echo ===============================================
echo.
echo الحلول المقترحة:
echo 1. تأكد من إصدار Node.js 18+
echo 2. تأكد من مساحة القرص الكافية (5GB+)
echo 3. أغلق مكافح الفيروسات مؤقتاً
echo 4. شغل Command Prompt كمدير
echo 5. تأكد من اتصال الإنترنت (للتحميل الأول)
echo 6. أعد تشغيل الجهاز وحاول مرة أخرى
echo.
if exist next.config.js.backup (
    copy next.config.js.backup next.config.js
    del next.config.js.backup
)
if exist package-installer.json del package-installer.json

:end
echo.
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo.
pause
