<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة الصفوة للنسيج - نظام إدارة موارد المؤسسة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #e0e7ff 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 20px 25px -5px rgba(37, 99, 235, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: 800;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 24px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .account-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .account-role {
            font-size: 16px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .account-email {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .account-password {
            font-size: 14px;
            color: #059669;
            font-weight: 500;
        }
        
        .install-steps {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
        }
        
        .install-title {
            font-size: 20px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .install-step {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-right: 4px solid #f59e0b;
        }
        
        .step-number {
            display: inline-block;
            background: #f59e0b;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .footer {
            text-align: center;
            color: #6b7280;
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.2s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon floating">
                    <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                        <path d="M6 10L16 6L26 10V22L16 26L6 22V10Z" stroke="currentColor" stroke-width="1.5" fill="rgba(255,255,255,0.1)"/>
                        <path d="M6 10L16 16L26 10" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M16 16V26" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="16" cy="16" r="2" fill="currentColor"/>
                        <path d="M10 13L22 13M10 16L22 16M10 19L22 19" stroke="currentColor" stroke-width="0.8" opacity="0.6"/>
                    </svg>
                </div>
                <div>
                    <div class="logo-text">شركة الصفوة للنسيج</div>
                    <div class="subtitle">نظام إدارة موارد المؤسسة المتكامل</div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">لوحة التحكم الذكية</div>
                <div class="feature-desc">إحصائيات شاملة ومؤشرات أداء في الوقت الفعلي مع تصميم عصري وتفاعلي</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🏭</div>
                <div class="feature-title">إدارة الإنتاج</div>
                <div class="feature-desc">تخطيط ومراقبة أوامر العمل ومراحل الإنتاج مع تتبع دقيق للجودة</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📦</div>
                <div class="feature-title">إدارة المخزون</div>
                <div class="feature-desc">تتبع المواد الخام والمنتجات مع تنبيهات ذكية للمخزون المنخفض</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🛒</div>
                <div class="feature-title">المشتريات والموردين</div>
                <div class="feature-desc">إدارة شاملة للموردين وأوامر الشراء مع تتبع التسليم والجودة</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💼</div>
                <div class="feature-title">المبيعات والعملاء</div>
                <div class="feature-desc">إدارة العملاء وأوامر البيع مع تتبع المبيعات والتحليلات</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">✅</div>
                <div class="feature-title">مراقبة الجودة</div>
                <div class="feature-desc">معايير جودة متقدمة مع فحوصات دورية وتقارير مفصلة</div>
            </div>
        </div>

        <!-- Demo Accounts -->
        <div class="demo-section">
            <div class="demo-title">🔐 الحسابات التجريبية</div>
            <div class="accounts-grid">
                <div class="account-card">
                    <div class="account-role">مدير النظام</div>
                    <div class="account-email"><EMAIL></div>
                    <div class="account-password">كلمة المرور: admin123</div>
                </div>
                
                <div class="account-card">
                    <div class="account-role">مشرف الإنتاج</div>
                    <div class="account-email"><EMAIL></div>
                    <div class="account-password">كلمة المرور: super123</div>
                </div>
                
                <div class="account-card">
                    <div class="account-role">مشغل الماكينة</div>
                    <div class="account-email"><EMAIL></div>
                    <div class="account-password">كلمة المرور: oper123</div>
                </div>
            </div>
        </div>

        <!-- Installation Steps -->
        <div class="install-steps">
            <div class="install-title">🚀 خطوات التشغيل السريع</div>
            
            <div class="install-step">
                <span class="step-number">1</span>
                <strong>تثبيت Node.js:</strong> حمل من nodejs.org واختر النسخة LTS
            </div>
            
            <div class="install-step">
                <span class="step-number">2</span>
                <strong>تثبيت PostgreSQL:</strong> حمل من postgresql.org للـ Windows
            </div>
            
            <div class="install-step">
                <span class="step-number">3</span>
                <strong>تشغيل النظام:</strong> افتح Command Prompt وشغل الأوامر التالية
            </div>
            
            <div style="background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 8px; margin-top: 15px; font-family: monospace;">
                npm install<br>
                copy .env.example .env<br>
                npm run dev
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="https://nodejs.org/" class="btn" target="_blank">تحميل Node.js</a>
                <a href="https://www.postgresql.org/download/windows/" class="btn" target="_blank">تحميل PostgreSQL</a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2024 شركة الصفوة للنسيج - نظام إدارة موارد المؤسسة</p>
            <p>تصميم عصري مع تقنيات متقدمة</p>
        </div>
    </div>
</body>
</html>
