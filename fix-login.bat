@echo off
chcp 65001 >nul
echo.
echo 🔧 حل مشاكل تسجيل الدخول - نظام الصفوة
echo ==========================================
echo.

echo [1/4] فحص حالة النظام...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ النظام يعمل
) else (
    echo ❌ النظام لا يعمل
    echo 🚀 تشغيل النظام...
    start cmd /c "cd /d %~dp0 && npm run dev"
    timeout /t 10 /nobreak >nul
)

echo.
echo [2/4] فحص الاتصال بالنظام...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ النظام متاح على http://localhost:3000
) else (
    echo ⚠️  النظام غير متاح على المنفذ 3000
    echo جرب المنافذ البديلة...
    
    curl -s http://localhost:3001 >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ النظام متاح على http://localhost:3001
        set PORT=3001
    ) else (
        curl -s http://localhost:3002 >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✅ النظام متاح على http://localhost:3002
            set PORT=3002
        ) else (
            echo ❌ النظام غير متاح على أي منفذ
            echo 🚀 إعادة تشغيل النظام...
            taskkill /F /IM node.exe 2>nul
            start cmd /c "cd /d %~dp0 && npm run dev"
            timeout /t 15 /nobreak >nul
            set PORT=3000
        )
    )
)

echo.
echo [3/4] إعداد قاعدة البيانات...
if exist "prisma\schema.prisma" (
    echo 📊 تطبيق مخطط قاعدة البيانات...
    cmd /c "npx prisma generate" >nul 2>&1
    cmd /c "npx prisma db push" >nul 2>&1
    
    echo 📊 إضافة البيانات التجريبية...
    cmd /c "npx prisma db seed" >nul 2>&1
    
    if %errorlevel% equ 0 (
        echo ✅ تم إعداد قاعدة البيانات بنجاح
    ) else (
        echo ⚠️  تم إعداد قاعدة البيانات الأساسية
    )
) else (
    echo ℹ️  لا توجد قاعدة بيانات - سيتم استخدام البيانات الافتراضية
)

echo.
echo [4/4] فتح النظام في المتصفح...
if defined PORT (
    start http://localhost:%PORT%
    echo 🌐 تم فتح النظام على: http://localhost:%PORT%
) else (
    start http://localhost:3000
    echo 🌐 تم فتح النظام على: http://localhost:3000
)

echo.
echo ==========================================
echo 🔐 بيانات تسجيل الدخول:
echo ==========================================
echo.
echo 👤 مدير النظام (صلاحيات كاملة):
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: admin123
echo.
echo 👤 مشرف الإنتاج:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: super123
echo.
echo 👤 مشغل الماكينة:
echo    📧 البريد: <EMAIL>
echo    🔑 كلمة المرور: oper123
echo.
echo ==========================================
echo 💡 نصائح لحل مشاكل تسجيل الدخول:
echo ==========================================
echo.
echo 1. تأكد من كتابة البريد الإلكتروني كاملاً
echo 2. كلمة المرور حساسة للأحرف الكبيرة والصغيرة
echo 3. جرب نسخ ولصق البيانات من هنا
echo 4. تأكد من عدم وجود مسافات إضافية
echo 5. أعد تحميل الصفحة (F5) إذا لم تعمل
echo.
echo ⚠️  إذا استمرت المشكلة:
echo - أعد تشغيل هذا الملف
echo - أو أعد تشغيل المتصفح
echo - أو جرب متصفح آخر
echo.
pause
