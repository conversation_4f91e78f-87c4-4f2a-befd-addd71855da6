// مخطط قاعدة البيانات المحلية - نظام الصفوة للنسيج
// SQLite Database Schema for Offline Mode

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./data/database.db"
}

// نموذج المستخدمين
model User {
  id          String   @id @default(cuid())
  name        String
  email       String   @unique
  password    String
  role        Role     @default(OPERATOR)
  department  String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  workOrders     WorkOrder[]
  qualityChecks  QualityCheck[]
  inventoryMoves InventoryMovement[]

  @@map("users")
}

// الأدوار
enum Role {
  ADMIN
  SUPERVISOR
  OPERATOR
}

// نموذج المواد الخام
model RawMaterial {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  category    String
  unit        String
  minStock    Float    @default(0)
  currentStock Float   @default(0)
  unitPrice   Float    @default(0)
  supplier    String?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  inventoryMoves InventoryMovement[]
  workOrderItems WorkOrderItem[]

  @@map("raw_materials")
}

// نموذج المنتجات
model Product {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  category    String
  size        String?
  color       String?
  unitPrice   Float    @default(0)
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  workOrders     WorkOrder[]
  qualityChecks  QualityCheck[]
  inventoryMoves InventoryMovement[]

  @@map("products")
}

// نموذج أوامر العمل
model WorkOrder {
  id           String        @id @default(cuid())
  orderNumber  String        @unique
  productId    String
  quantity     Int
  status       WorkOrderStatus @default(PENDING)
  priority     Priority      @default(MEDIUM)
  startDate    DateTime?
  endDate      DateTime?
  completedAt  DateTime?
  assignedToId String?
  notes        String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // العلاقات
  product      Product       @relation(fields: [productId], references: [id])
  assignedTo   User?         @relation(fields: [assignedToId], references: [id])
  items        WorkOrderItem[]
  qualityChecks QualityCheck[]

  @@map("work_orders")
}

// عناصر أوامر العمل
model WorkOrderItem {
  id             String      @id @default(cuid())
  workOrderId    String
  rawMaterialId  String
  quantityNeeded Float
  quantityUsed   Float       @default(0)
  createdAt      DateTime    @default(now())

  // العلاقات
  workOrder    WorkOrder   @relation(fields: [workOrderId], references: [id], onDelete: Cascade)
  rawMaterial  RawMaterial @relation(fields: [rawMaterialId], references: [id])

  @@map("work_order_items")
}

// حالات أوامر العمل
enum WorkOrderStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// الأولوية
enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// نموذج فحوصات الجودة
model QualityCheck {
  id          String        @id @default(cuid())
  workOrderId String?
  productId   String?
  checkerId   String
  checkType   String
  result      QualityResult
  score       Float?
  defects     String?
  notes       String?
  checkedAt   DateTime      @default(now())
  createdAt   DateTime      @default(now())

  // العلاقات
  workOrder WorkOrder? @relation(fields: [workOrderId], references: [id])
  product   Product?   @relation(fields: [productId], references: [id])
  checker   User       @relation(fields: [checkerId], references: [id])

  @@map("quality_checks")
}

// نتائج الجودة
enum QualityResult {
  PASS
  FAIL
  CONDITIONAL
}

// نموذج حركات المخزون
model InventoryMovement {
  id             String         @id @default(cuid())
  rawMaterialId  String?
  productId      String?
  movementType   MovementType
  quantity       Float
  unitPrice      Float?
  totalValue     Float?
  reference      String?
  notes          String?
  userId         String
  createdAt      DateTime       @default(now())

  // العلاقات
  rawMaterial RawMaterial? @relation(fields: [rawMaterialId], references: [id])
  product     Product?     @relation(fields: [productId], references: [id])
  user        User         @relation(fields: [userId], references: [id])

  @@map("inventory_movements")
}

// أنواع حركات المخزون
enum MovementType {
  IN
  OUT
  ADJUSTMENT
  TRANSFER
}

// نموذج الموردين
model Supplier {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  contactName String?
  phone       String?
  email       String?
  address     String?
  city        String?
  country     String?
  rating      Float?   @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("suppliers")
}

// نموذج العملاء
model Customer {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  contactName String?
  phone       String?
  email       String?
  address     String?
  city        String?
  country     String?
  creditLimit Float?   @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("customers")
}

// نموذج الماكينات
model Machine {
  id           String        @id @default(cuid())
  name         String
  code         String        @unique
  type         String
  manufacturer String?
  model        String?
  status       MachineStatus @default(IDLE)
  location     String?
  capacity     Float?
  efficiency   Float?        @default(100)
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  @@map("machines")
}

// حالات الماكينات
enum MachineStatus {
  IDLE
  RUNNING
  MAINTENANCE
  BREAKDOWN
  OFFLINE
}

// نموذج الإعدادات
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  category  String?
  description String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
