@echo off
title نظام الصفوة للنسيج - التشغيل السريع
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           نظام الصفوة للنسيج - التشغيل السريع
echo                              🚀 ابدأ الآن بنقرة واحدة 🚀
echo.
echo ===============================================================================
echo.

echo مرحباً بك في نظام الصفوة لإدارة مصانع النسيج!
echo.
echo اختر طريقة التشغيل المفضلة:
echo.
echo [1] التشغيل السريع (وضع المتصفح - موصى به)
echo [2] تطبيق سطح المكتب (Electron)
echo [3] إنشاء ملفات التثبيت
echo [4] عرض معلومات النظام
echo [5] خروج
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto browser_mode
if "%choice%"=="2" goto desktop_mode
if "%choice%"=="3" goto create_installer
if "%choice%"=="4" goto system_info
if "%choice%"=="5" goto exit
echo خيار غير صحيح!
pause
goto start

:browser_mode
echo.
echo ===============================================
echo تشغيل وضع المتصفح
echo ===============================================
echo.
echo جاري تشغيل نظام الصفوة في وضع المتصفح...
echo هذا الوضع يوفر أفضل استقرار وأداء.
echo.
echo [1/3] تحضير البيئة...
set NODE_ENV=development

echo [2/3] بدء الخادم...
start /B npm run dev

echo [3/3] انتظار تحميل النظام...
timeout /t 8 /nobreak >nul

echo فتح النظام في المتصفح...
start http://localhost:3000

echo.
echo ===============================================
echo ✓ تم تشغيل النظام بنجاح!
echo ===============================================
echo.
echo العنوان: http://localhost:3000
echo تسجيل الدخول: admin / admin123
echo.
echo المميزات:
echo ✓ يعمل بدون إنترنت
echo ✓ جميع وحدات النسيج متاحة
echo ✓ واجهة عربية احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ أداء فائق وسرعة عالية
echo.
goto end

:desktop_mode
echo.
echo ===============================================
echo تشغيل تطبيق سطح المكتب
echo ===============================================
echo.
echo جاري تشغيل نظام الصفوة كتطبيق سطح مكتب...
echo.
if exist "SAFA-STANDALONE-FINAL.bat" (
    call SAFA-STANDALONE-FINAL.bat
) else (
    echo تشغيل Electron...
    npm list electron >nul 2>&1
    if errorlevel 1 (
        echo تثبيت Electron...
        npm install electron --save-dev
    )
    start /B npm run dev
    timeout /t 8 /nobreak >nul
    npx electron .
)
goto end

:create_installer
echo.
echo ===============================================
echo إنشاء ملفات التثبيت
echo ===============================================
echo.
echo اختر نوع التثبيت:
echo [1] المثبت الاحترافي
echo [2] النسخة المحمولة
echo [3] كلاهما (شامل)
echo.
set /p install_choice="اختر رقم (1-3): "

if "%install_choice%"=="1" (
    if exist "create-installer.bat" (
        call create-installer.bat
    ) else (
        echo ملف create-installer.bat غير موجود
    )
) else if "%install_choice%"=="2" (
    if exist "create-portable-version.bat" (
        call create-portable-version.bat
    ) else (
        echo ملف create-portable-version.bat غير موجود
    )
) else if "%install_choice%"=="3" (
    if exist "auto-installer.bat" (
        call auto-installer.bat
    ) else (
        echo ملف auto-installer.bat غير موجود
    )
) else (
    echo خيار غير صحيح!
)
goto end

:system_info
echo.
echo ===============================================
echo معلومات نظام الصفوة للنسيج
echo ===============================================
echo.
echo 🏭 نظام إدارة مصانع النسيج الشامل
echo الإصدار: 1.0.0
echo المطور: شركة الصفوة للنسيج
echo.
echo ✨ المميزات الرئيسية:
echo ✓ يعمل بدون إنترنت
echo ✓ واجهة عربية احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ بيانات تجريبية واقعية
echo ✓ أداء فائق وسرعة عالية
echo ✓ تصميم متجاوب لجميع الأحجام
echo.
echo 🏭 الوحدات المتاحة:
echo • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo • المالية: الحسابات، الفواتير، التقارير المالية
echo • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo 🔐 تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 💻 متطلبات النظام:
echo - Windows 10/11
echo - Node.js 18+ (للتطوير)
echo - 4GB RAM (الحد الأدنى)
echo - 2GB مساحة قرص
echo.
echo 📋 ملفات التشغيل المتاحة:
if exist "run-browser-mode.bat" echo ✓ run-browser-mode.bat (وضع المتصفح)
if exist "SAFA-STANDALONE-FINAL.bat" echo ✓ SAFA-STANDALONE-FINAL.bat (سطح المكتب)
if exist "create-installer.bat" echo ✓ create-installer.bat (إنشاء مثبت)
if exist "create-portable-version.bat" echo ✓ create-portable-version.bat (نسخة محمولة)
echo.
echo 📞 الدعم الفني:
echo البريد الإلكتروني: <EMAIL>
echo الموقع: www.safa-textile.com
echo.
echo حقوق الطبع والنشر © 2024 شركة الصفوة للنسيج
echo.
pause
goto start

:exit
echo.
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo.
goto end

:start
cls
goto :eof

:end
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
