import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Factory, Package, ShoppingCart, TrendingUp } from 'lucide-react'

const stats = [
  {
    title: 'أوامر العمل النشطة',
    value: '12',
    change: '+2.5%',
    changeType: 'positive' as const,
    icon: Factory,
  },
  {
    title: 'المواد في المخزون',
    value: '1,234',
    change: '-5.2%',
    changeType: 'negative' as const,
    icon: Package,
  },
  {
    title: 'أوامر الشراء المعلقة',
    value: '8',
    change: '+12.3%',
    changeType: 'positive' as const,
    icon: ShoppingCart,
  },
  {
    title: 'الإنتاجية اليومية',
    value: '95%',
    change: '+3.1%',
    changeType: 'positive' as const,
    icon: TrendingUp,
  },
]

export function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : 'text-red-600'
            }`}>
              {stat.change} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
