@echo off
title إنشاء النسخة المحمولة - نظام الصفوة للنسيج
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء النسخة المحمولة - نظام الصفوة للنسيج
echo                              Portable Version Creator
echo.
echo ===============================================================================
echo.

echo [1/8] تحضير مجلد النسخة المحمولة...
set PORTABLE_DIR=Safa-Textile-Portable
if exist "%PORTABLE_DIR%" rmdir /s /q "%PORTABLE_DIR%"
mkdir "%PORTABLE_DIR%"

echo [2/8] نسخ الملفات الأساسية...
echo نسخ ملفات المشروع...
xcopy "src" "%PORTABLE_DIR%\src" /E /I /Q
xcopy "public" "%PORTABLE_DIR%\public" /E /I /Q
xcopy "electron" "%PORTABLE_DIR%\electron" /E /I /Q
xcopy "data" "%PORTABLE_DIR%\data" /E /I /Q 2>nul

echo [3/8] نسخ ملفات التكوين...
copy "package.json" "%PORTABLE_DIR%\" 2>nul
copy "next.config.js" "%PORTABLE_DIR%\" 2>nul
copy "tailwind.config.ts" "%PORTABLE_DIR%\" 2>nul
copy "tsconfig.json" "%PORTABLE_DIR%\" 2>nul
copy "postcss.config.js" "%PORTABLE_DIR%\" 2>nul

echo [4/8] إنشاء ملف التشغيل المحمول...
(
echo @echo off
echo title نظام الصفوة للنسيج - النسخة المحمولة
echo color 0A
echo echo.
echo echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo echo.
echo echo                           نظام الصفوة للنسيج - النسخة المحمولة
echo echo                              🖥️ يعمل من أي مكان بدون تثبيت 🖥️
echo echo.
echo echo ===============================================================================
echo echo.
echo echo تحضير النظام...
echo cd /d "%%~dp0"
echo.
echo echo التحقق من Node.js...
echo node --version ^>nul 2^>^&1
echo if errorlevel 1 ^(
echo     echo ✗ Node.js غير مثبت على هذا الجهاز
echo     echo.
echo     echo يرجى تثبيت Node.js أولاً من:
echo     echo https://nodejs.org
echo     echo.
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo ✓ Node.js متوفر
echo echo.
echo echo تثبيت المكتبات المطلوبة...
echo npm install
echo.
echo echo تشغيل نظام الصفوة للنسيج...
echo echo.
echo echo ===============================================
echo echo نظام الصفوة للنسيج - النسخة المحمولة
echo echo ===============================================
echo echo.
echo echo المميزات:
echo echo ✓ يعمل من أي مجلد
echo echo ✓ لا يحتاج تثبيت
echo echo ✓ يعمل بدون إنترنت
echo echo ✓ جميع الوحدات متاحة
echo echo.
echo echo تسجيل الدخول:
echo echo اسم المستخدم: admin
echo echo كلمة المرور: admin123
echo echo.
echo echo ===============================================
echo echo.
echo start http://localhost:3000
echo npm run dev
) > "%PORTABLE_DIR%\تشغيل-نظام-الصفوة.bat"

echo [5/8] إنشاء ملف التثبيت السريع...
(
echo @echo off
echo title تثبيت سريع - نظام الصفوة للنسيج
echo echo تثبيت المكتبات المطلوبة...
echo npm install
echo echo.
echo echo ✓ تم التثبيت بنجاح!
echo echo يمكنك الآن تشغيل النظام من ملف "تشغيل-نظام-الصفوة.bat"
echo pause
) > "%PORTABLE_DIR%\تثبيت-سريع.bat"

echo [6/8] إنشاء ملف معلومات النظام...
(
echo ===============================================
echo نظام الصفوة لإدارة مصانع النسيج
echo النسخة المحمولة v1.0
echo ===============================================
echo.
echo المميزات:
echo ✓ يعمل من أي مجلد بدون تثبيت
echo ✓ يعمل بدون إنترنت
echo ✓ جميع وحدات النسيج متاحة
echo ✓ واجهة عربية احترافية
echo ✓ بيانات تجريبية مدمجة
echo.
echo المتطلبات:
echo - Windows 10/11
echo - Node.js 18+ ^(سيتم تثبيته تلقائياً^)
echo - 2GB مساحة قرص
echo.
echo طريقة التشغيل:
echo 1. شغل "تثبيت-سريع.bat" ^(أول مرة فقط^)
echo 2. شغل "تشغيل-نظام-الصفوة.bat"
echo 3. سجل دخول: admin / admin123
echo.
echo الوحدات المتاحة:
echo • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo • المالية: الحسابات، الفواتير، التقارير المالية
echo • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo الدعم الفني:
echo البريد الإلكتروني: <EMAIL>
echo الموقع: www.safa-textile.com
echo.
echo حقوق الطبع والنشر © 2024 شركة الصفوة للنسيج
echo ===============================================
) > "%PORTABLE_DIR%\اقرأني.txt"

echo [7/8] ضغط النسخة المحمولة...
if exist "Safa-Textile-Portable.zip" del "Safa-Textile-Portable.zip"
powershell -Command "Compress-Archive -Path '%PORTABLE_DIR%' -DestinationPath 'Safa-Textile-Portable.zip'"

echo [8/8] التحقق من النتيجة...
if exist "Safa-Textile-Portable.zip" (
    echo.
    echo ===============================================
    echo ✓ تم إنشاء النسخة المحمولة بنجاح!
    echo ===============================================
    echo.
    echo الملفات المُنشأة:
    echo 📁 مجلد: %PORTABLE_DIR%
    echo 📦 ملف مضغوط: Safa-Textile-Portable.zip
    echo.
    echo محتويات النسخة المحمولة:
    dir "%PORTABLE_DIR%" /b
    echo.
    echo حجم الملف المضغوط:
    for %%i in (Safa-Textile-Portable.zip) do echo %%~zi bytes
    echo.
    echo المميزات:
    echo ✓ تعمل من أي مجلد
    echo ✓ لا تحتاج تثبيت
    echo ✓ تعمل بدون إنترنت
    echo ✓ سهلة النقل والتوزيع
    echo ✓ جميع الوحدات مدمجة
    echo.
    echo طريقة الاستخدام:
    echo 1. فك ضغط الملف في أي مكان
    echo 2. شغل "تثبيت-سريع.bat" ^(أول مرة^)
    echo 3. شغل "تشغيل-نظام-الصفوة.bat"
    echo.
) else (
    echo ✗ فشل في إنشاء النسخة المحمولة
    echo تحقق من الصلاحيات ومساحة القرص
)

echo.
echo ===============================================
echo اكتمل إنشاء النسخة المحمولة!
echo ===============================================
echo.
pause
