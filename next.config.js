/** @type {import('next').NextConfig} */
const nextConfig = {
  // إعدادات للعمل بدون إنترنت
  output: 'standalone',
  
  // تحسين الأداء للوضع المحلي
  experimental: {
    optimizeCss: true,
    optimizeServerReact: true,
  },
  
  // إعدادات الصور للوضع المحلي
  images: {
    unoptimized: true,
    domains: ['localhost']
  },
  
  // إعدادات الخطوط المحلية
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // تحسين للوضع المحلي
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
    }
    
    // إضافة الخطوط المحلية
    config.module.rules.push({
      test: /\.(woff|woff2|eot|ttf|otf)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/fonts/',
          outputPath: 'static/fonts/',
        },
      },
    })
    
    return config
  },
  
  // إعدادات البيئة للوضع المحلي
  env: {
    OFFLINE_MODE: 'true',
    DATABASE_TYPE: 'sqlite',
    CUSTOM_KEY: 'safa-textile-offline',
  },
  
  // تعطيل التحليلات والتتبع
  analyticsId: '',
  
  // إعدادات الأمان للوضع المحلي
  poweredByHeader: false,
  
  // ضغط الملفات
  compress: true,
  
  // إعدادات التوجيه للوضع المحلي
  async redirects() {
    return [
      {
        source: '/api/external/:path*',
        destination: '/api/offline/:path*',
        permanent: false,
      },
    ]
  },
  
  // رؤوس الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;",
          },
        ],
      },
    ]
  },
}

module.exports = nextConfig
