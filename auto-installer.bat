@echo off
title مثبت نظام الصفوة للنسيج التلقائي
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           مثبت نظام الصفوة للنسيج التلقائي
echo                              🚀 تثبيت شامل بنقرة واحدة 🚀
echo.
echo ===============================================================================
echo.

echo مرحباً بك في مثبت نظام الصفوة للنسيج التلقائي!
echo.
echo هذا المثبت سيقوم بـ:
echo ✓ إنشاء المثبت الاحترافي
echo ✓ إنشاء النسخة المحمولة
echo ✓ إنشاء ملفات التشغيل
echo ✓ إنشاء الأدلة والوثائق
echo.
echo اختر نوع التثبيت:
echo.
echo [1] المثبت الاحترافي (موصى به للشركات)
echo [2] النسخة المحمولة (موصى به للتجريب)
echo [3] كلاهما (شامل)
echo [4] إلغاء
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto professional
if "%choice%"=="2" goto portable
if "%choice%"=="3" goto both
if "%choice%"=="4" goto cancel
echo خيار غير صحيح!
pause
exit /b 1

:professional
echo.
echo ===============================================
echo إنشاء المثبت الاحترافي
echo ===============================================
echo.
call create-installer.bat
goto success

:portable
echo.
echo ===============================================
echo إنشاء النسخة المحمولة
echo ===============================================
echo.
call create-portable-version.bat
goto success

:both
echo.
echo ===============================================
echo إنشاء كلا الإصدارين
echo ===============================================
echo.
echo [1/2] إنشاء المثبت الاحترافي...
call create-installer.bat
echo.
echo [2/2] إنشاء النسخة المحمولة...
call create-portable-version.bat
goto success

:success
echo.
echo ===============================================
echo ✓ تم إكمال التثبيت بنجاح!
echo ===============================================
echo.

if exist "dist" (
    echo 📦 المثبت الاحترافي:
    for /r dist %%i in (*.exe) do (
        echo    ملف: %%~nxi
        echo    الحجم: %%~zi bytes
        echo    المسار: %%i
    )
    echo.
)

if exist "Safa-Textile-Portable.zip" (
    echo 🎒 النسخة المحمولة:
    for %%i in (Safa-Textile-Portable.zip) do (
        echo    ملف: %%~nxi
        echo    الحجم: %%~zi bytes
    )
    echo.
)

echo 📋 ملفات التشغيل المتاحة:
if exist "SAFA-STANDALONE-FINAL.bat" echo    ✓ SAFA-STANDALONE-FINAL.bat
if exist "run-browser-mode.bat" echo    ✓ run-browser-mode.bat
if exist "run-simple-standalone.bat" echo    ✓ run-simple-standalone.bat

echo.
echo 📚 الأدلة والوثائق:
if exist "INSTALLATION_GUIDE.md" echo    ✓ دليل التثبيت
if exist "FINAL_STANDALONE_GUIDE.md" echo    ✓ دليل التطبيق المستقل
if exist "ERROR_SOLUTIONS_GUIDE.md" echo    ✓ دليل حل الأخطاء

echo.
echo ===============================================
echo طرق التشغيل المتاحة:
echo ===============================================
echo.
echo 🏆 للاستخدام الاحترافي:
if exist "dist" (
    echo    1. شغل ملف المثبت من مجلد dist
    echo    2. اتبع تعليمات التثبيت
    echo    3. ابحث عن الاختصار في سطح المكتب
)

echo.
echo 🎒 للاستخدام المحمول:
if exist "Safa-Textile-Portable.zip" (
    echo    1. فك ضغط ملف Safa-Textile-Portable.zip
    echo    2. شغل "تثبيت-سريع.bat" (أول مرة)
    echo    3. شغل "تشغيل-نظام-الصفوة.bat"
)

echo.
echo ⚡ للتشغيل السريع:
echo    1. شغل SAFA-STANDALONE-FINAL.bat
echo    2. أو شغل run-browser-mode.bat
echo.
echo 🔐 تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ===============================================
echo معلومات النظام
echo ===============================================
echo.
echo 🏭 الوحدات المتاحة:
echo    • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo    • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo    • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo    • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo    • المالية: الحسابات، الفواتير، التقارير المالية
echo    • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo    • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo ✨ المميزات:
echo    ✓ يعمل بدون إنترنت
echo    ✓ واجهة عربية احترافية
echo    ✓ شعار الصفوة في جميع الصفحات
echo    ✓ بيانات تجريبية واقعية
echo    ✓ أداء فائق وسرعة عالية
echo.
echo 📞 الدعم الفني:
echo    البريد الإلكتروني: <EMAIL>
echo    الموقع: www.safa-textile.com
echo.
echo ===============================================
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo ===============================================
goto end

:cancel
echo تم إلغاء التثبيت.
goto end

:end
echo.
pause
