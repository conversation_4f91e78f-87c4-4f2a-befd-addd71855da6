'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  ShoppingCart,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface PurchaseRequestItem {
  id: string
  materialCode: string
  materialName: string
  requestedQuantity: number
  unit: string
  estimatedPrice: number
  totalEstimated: number
  urgency: string
  notes?: string
}

interface PurchaseRequest {
  id: string
  requestNumber: string
  title: string
  department: string
  requestedBy: string
  requestDate: Date
  requiredDate: Date
  status: string
  priority: string
  totalEstimated: number
  approvedBy?: string
  approvedDate?: Date
  rejectionReason?: string
  items: PurchaseRequestItem[]
  notes?: string
}

const mockRequests: PurchaseRequest[] = [
  {
    id: '1',
    requestNumber: 'PR-2024-001',
    title: 'طل<PERSON> مواد خام للإنتاج الشهري',
    department: 'الإنتاج',
    requestedBy: 'أحمد محمد',
    requestDate: new Date('2024-01-15'),
    requiredDate: new Date('2024-01-25'),
    status: 'PENDING',
    priority: 'HIGH',
    totalEstimated: 25000,
    items: [
      {
        id: '1',
        materialCode: 'MAT001',
        materialName: 'قطن خام عالي الجودة',
        requestedQuantity: 1000,
        unit: 'كيلو',
        estimatedPrice: 15.50,
        totalEstimated: 15500,
        urgency: 'HIGH',
        notes: 'مطلوب للإنتاج العاجل'
      },
      {
        id: '2',
        materialCode: 'MAT002',
        materialName: 'صبغة زرقاء',
        requestedQuantity: 200,
        unit: 'لتر',
        estimatedPrice: 45.00,
        totalEstimated: 9000,
        urgency: 'MEDIUM'
      }
    ],
    notes: 'طلب عاجل للإنتاج'
  },
  {
    id: '2',
    requestNumber: 'PR-2024-002',
    title: 'طلب قطع غيار للماكينات',
    department: 'الصيانة',
    requestedBy: 'فاطمة علي',
    requestDate: new Date('2024-01-16'),
    requiredDate: new Date('2024-01-30'),
    status: 'APPROVED',
    priority: 'MEDIUM',
    totalEstimated: 8500,
    approvedBy: 'مدير المشتريات',
    approvedDate: new Date('2024-01-17'),
    items: [
      {
        id: '3',
        materialCode: 'SP001',
        materialName: 'حزام نقل للنول',
        requestedQuantity: 5,
        unit: 'قطعة',
        estimatedPrice: 1200,
        totalEstimated: 6000,
        urgency: 'MEDIUM'
      },
      {
        id: '4',
        materialCode: 'SP002',
        materialName: 'فلتر هواء للماكينة',
        requestedQuantity: 10,
        unit: 'قطعة',
        estimatedPrice: 250,
        totalEstimated: 2500,
        urgency: 'LOW'
      }
    ]
  },
  {
    id: '3',
    requestNumber: 'PR-2024-003',
    title: 'طلب مواد تنظيف ومطهرات',
    department: 'النظافة',
    requestedBy: 'محمد أحمد',
    requestDate: new Date('2024-01-17'),
    requiredDate: new Date('2024-02-01'),
    status: 'REJECTED',
    priority: 'LOW',
    totalEstimated: 1500,
    rejectionReason: 'يوجد مخزون كافي من هذه المواد',
    items: [
      {
        id: '5',
        materialCode: 'CL001',
        materialName: 'مطهر عام',
        requestedQuantity: 20,
        unit: 'لتر',
        estimatedPrice: 75,
        totalEstimated: 1500,
        urgency: 'LOW'
      }
    ]
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING': return 'bg-yellow-500'
    case 'APPROVED': return 'bg-green-500'
    case 'REJECTED': return 'bg-red-500'
    case 'CONVERTED': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return 'في الانتظار'
    case 'APPROVED': return 'معتمد'
    case 'REJECTED': return 'مرفوض'
    case 'CONVERTED': return 'تم التحويل'
    default: return 'غير محدد'
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'bg-red-500'
    case 'MEDIUM': return 'bg-yellow-500'
    case 'LOW': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'عالية'
    case 'MEDIUM': return 'متوسطة'
    case 'LOW': return 'منخفضة'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PENDING': return <Clock className="h-4 w-4" />
    case 'APPROVED': return <CheckCircle className="h-4 w-4" />
    case 'REJECTED': return <XCircle className="h-4 w-4" />
    case 'CONVERTED': return <ShoppingCart className="h-4 w-4" />
    default: return <AlertTriangle className="h-4 w-4" />
  }
}

export default function PurchaseRequestsPage() {
  const [requests, setRequests] = useState<PurchaseRequest[]>(mockRequests)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedPriority, setSelectedPriority] = useState<string>('all')

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.requestNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.requestedBy.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || request.status === selectedStatus
    const matchesPriority = selectedPriority === 'all' || request.priority === selectedPriority
    return matchesSearch && matchesStatus && matchesPriority
  })

  const handleApprove = (requestId: string) => {
    setRequests(requests.map(request => 
      request.id === requestId 
        ? { 
            ...request, 
            status: 'APPROVED',
            approvedBy: 'المستخدم الحالي',
            approvedDate: new Date()
          }
        : request
    ))
  }

  const handleReject = (requestId: string, reason: string) => {
    setRequests(requests.map(request => 
      request.id === requestId 
        ? { 
            ...request, 
            status: 'REJECTED',
            rejectionReason: reason
          }
        : request
    ))
  }

  const totalPending = requests.filter(r => r.status === 'PENDING').length
  const totalApproved = requests.filter(r => r.status === 'APPROVED').length
  const totalValue = requests.reduce((acc, r) => acc + r.totalEstimated, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">طلبات الشراء</h1>
          <p className="text-gray-600">إدارة ومتابعة طلبات الشراء من الأقسام المختلفة</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            طلب جديد
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                <p className="text-2xl font-bold text-blue-600">{requests.length}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">{totalPending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معتمد</p>
                <p className="text-2xl font-bold text-green-600">{totalApproved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي القيمة</p>
                <p className="text-2xl font-bold text-purple-600">
                  {totalValue.toLocaleString()} ر.س
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الطلبات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="PENDING">في الانتظار</option>
                <option value="APPROVED">معتمد</option>
                <option value="REJECTED">مرفوض</option>
                <option value="CONVERTED">تم التحويل</option>
              </select>
              <select
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأولويات</option>
                <option value="HIGH">عالية</option>
                <option value="MEDIUM">متوسطة</option>
                <option value="LOW">منخفضة</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة طلبات الشراء</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredRequests.map((request) => (
              <div key={request.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(request.status)}>
                      {getStatusIcon(request.status)}
                      <span className="mr-1">{getStatusText(request.status)}</span>
                    </Badge>
                    <Badge className={getPriorityColor(request.priority)}>
                      {getPriorityText(request.priority)}
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{request.title}</h3>
                      <p className="text-sm text-gray-600">
                        {request.requestNumber} - {request.department}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {request.status === 'PENDING' && (
                      <>
                        <Button 
                          size="sm"
                          onClick={() => handleApprove(request.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 ml-1" />
                          اعتماد
                        </Button>
                        <Button 
                          size="sm"
                          variant="outline"
                          onClick={() => handleReject(request.id, 'مرفوض من قبل المستخدم')}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="h-4 w-4 ml-1" />
                          رفض
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">طالب الشراء</p>
                    <p className="font-medium">{request.requestedBy}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">تاريخ الطلب</p>
                    <p className="font-medium">{request.requestDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">التاريخ المطلوب</p>
                    <p className="font-medium">{request.requiredDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">القيمة المقدرة</p>
                    <p className="font-medium">{request.totalEstimated.toLocaleString()} ر.س</p>
                  </div>
                </div>

                {/* Items Summary */}
                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-2">الأصناف المطلوبة ({request.items.length}):</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {request.items.slice(0, 2).map((item) => (
                      <div key={item.id} className="text-sm bg-gray-100 rounded p-2">
                        <span className="font-medium">{item.materialName}</span>
                        <span className="text-gray-600"> - {item.requestedQuantity} {item.unit}</span>
                      </div>
                    ))}
                    {request.items.length > 2 && (
                      <div className="text-sm text-gray-500 p-2">
                        و {request.items.length - 2} أصناف أخرى...
                      </div>
                    )}
                  </div>
                </div>

                {/* Approval/Rejection Info */}
                {request.status === 'APPROVED' && request.approvedBy && (
                  <div className="mt-3 p-2 bg-green-100 rounded text-sm">
                    <strong>معتمد بواسطة:</strong> {request.approvedBy} في {request.approvedDate?.toLocaleDateString('ar-SA')}
                  </div>
                )}

                {request.status === 'REJECTED' && request.rejectionReason && (
                  <div className="mt-3 p-2 bg-red-100 rounded text-sm">
                    <strong>سبب الرفض:</strong> {request.rejectionReason}
                  </div>
                )}

                {request.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {request.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredRequests.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد طلبات شراء تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
