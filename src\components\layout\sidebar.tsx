'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Package,
  Factory,
  ShoppingCart,
  Users,
  ClipboardCheck,
  DollarSign,
  UserCheck,
  Settings,
  BarChart3,
  Truck,
  AlertTriangle,
} from 'lucide-react'

const menuItems = [
  {
    title: 'لوحة التحكم',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'الإنتاج',
    icon: Factory,
    children: [
      { title: 'أوامر العمل', href: '/dashboard/production/work-orders' },
      { title: 'مراحل الإنتاج', href: '/dashboard/production/stages' },
      { title: 'تخطيط الإنتاج', href: '/dashboard/production/planning' },
    ],
  },
  {
    title: 'المخزون',
    icon: Package,
    children: [
      { title: 'المواد الخام', href: '/dashboard/inventory/materials' },
      { title: 'المنتجات', href: '/dashboard/inventory/products' },
      { title: 'حركات المخزون', href: '/dashboard/inventory/movements' },
      { title: 'الجرد', href: '/dashboard/inventory/stock-take' },
    ],
  },
  {
    title: 'المشتريات',
    icon: ShoppingCart,
    children: [
      { title: 'أوامر الشراء', href: '/dashboard/purchasing/orders' },
      { title: 'الموردين', href: '/dashboard/purchasing/suppliers' },
      { title: 'طلبات الشراء', href: '/dashboard/purchasing/requests' },
    ],
  },
  {
    title: 'المبيعات',
    icon: Truck,
    children: [
      { title: 'أوامر البيع', href: '/dashboard/sales/orders' },
      { title: 'العملاء', href: '/dashboard/sales/customers' },
      { title: 'عروض الأسعار', href: '/dashboard/sales/quotes' },
    ],
  },
  {
    title: 'الجودة',
    href: '/dashboard/quality',
    icon: ClipboardCheck,
  },
  {
    title: 'المالية',
    icon: DollarSign,
    children: [
      { title: 'الحسابات', href: '/dashboard/finance/accounts' },
      { title: 'الفواتير', href: '/dashboard/finance/invoices' },
      { title: 'التقارير المالية', href: '/dashboard/finance/reports' },
    ],
  },
  {
    title: 'الموارد البشرية',
    icon: UserCheck,
    children: [
      { title: 'الموظفين', href: '/dashboard/hr/employees' },
      { title: 'الحضور والانصراف', href: '/dashboard/hr/attendance' },
      { title: 'الرواتب', href: '/dashboard/hr/payroll' },
    ],
  },
  {
    title: 'الصيانة',
    href: '/dashboard/maintenance',
    icon: AlertTriangle,
  },
  {
    title: 'التقارير',
    href: '/dashboard/reports',
    icon: BarChart3,
  },
  {
    title: 'إدارة المستخدمين',
    href: '/dashboard/users',
    icon: Users,
  },
  {
    title: 'الإعدادات',
    href: '/dashboard/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-card border-l border-border h-screen overflow-y-auto">
      <div className="p-6">
        <h2 className="text-xl font-bold text-primary">نظام إدارة النسيج</h2>
      </div>

      <nav className="px-4 pb-4">
        <ul className="space-y-2">
          {menuItems.map((item, index) => (
            <li key={index}>
              {item.children ? (
                <div>
                  <div className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-muted-foreground">
                    <item.icon className="h-4 w-4" />
                    {item.title}
                  </div>
                  <ul className="mr-4 mt-1 space-y-1">
                    {item.children.map((child, childIndex) => (
                      <li key={childIndex}>
                        <Link
                          href={child.href}
                          className={cn(
                            'block px-3 py-2 text-sm rounded-lg transition-colors',
                            pathname === child.href
                              ? 'bg-primary text-primary-foreground'
                              : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                          )}
                        >
                          {child.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <Link
                  href={item.href!}
                  className={cn(
                    'flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors',
                    pathname === item.href
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  {item.title}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  )
}
