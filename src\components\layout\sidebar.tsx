'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Logo } from '@/components/ui/logo'
import {
  LayoutDashboard,
  Package,
  Factory,
  ShoppingCart,
  Users,
  ClipboardCheck,
  DollarSign,
  UserCheck,
  Settings,
  BarChart3,
  Truck,
  AlertTriangle,
} from 'lucide-react'

const menuItems = [
  {
    title: 'لوحة التحكم',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'الإنتاج',
    icon: Factory,
    children: [
      { title: 'أوامر العمل', href: '/dashboard/production/work-orders' },
      { title: 'مراحل الإنتاج', href: '/dashboard/production/stages' },
      { title: 'تخطيط الإنتاج', href: '/dashboard/production/planning' },
    ],
  },
  {
    title: 'المخزون',
    icon: Package,
    children: [
      { title: 'المواد الخام', href: '/dashboard/inventory/materials' },
      { title: 'المنتجات', href: '/dashboard/inventory/products' },
      { title: 'حركات المخزون', href: '/dashboard/inventory/movements' },
      { title: 'الجرد', href: '/dashboard/inventory/count' },
    ],
  },
  {
    title: 'المشتريات',
    icon: ShoppingCart,
    children: [
      { title: 'أوامر الشراء', href: '/dashboard/purchasing/orders' },
      { title: 'الموردين', href: '/dashboard/purchasing/suppliers' },
      { title: 'طلبات الشراء', href: '/dashboard/purchasing/requests' },
    ],
  },
  {
    title: 'المبيعات',
    icon: Truck,
    children: [
      { title: 'أوامر البيع', href: '/dashboard/sales/orders' },
      { title: 'العملاء', href: '/dashboard/sales/customers' },
      { title: 'عروض الأسعار', href: '/dashboard/sales/quotes' },
    ],
  },
  {
    title: 'الجودة',
    href: '/dashboard/quality',
    icon: ClipboardCheck,
  },
  {
    title: 'المالية',
    icon: DollarSign,
    children: [
      { title: 'الحسابات', href: '/dashboard/finance/accounts' },
      { title: 'الفواتير', href: '/dashboard/finance/invoices' },
      { title: 'التقارير المالية', href: '/dashboard/finance/reports' },
    ],
  },
  {
    title: 'الموارد البشرية',
    icon: UserCheck,
    children: [
      { title: 'الموظفين', href: '/dashboard/hr/employees' },
      { title: 'الحضور والانصراف', href: '/dashboard/hr/attendance' },
      { title: 'الرواتب', href: '/dashboard/hr/payroll' },
    ],
  },
  {
    title: 'الصيانة',
    href: '/dashboard/maintenance',
    icon: AlertTriangle,
  },
  {
    title: 'التقارير',
    href: '/dashboard/reports',
    icon: BarChart3,
  },
  {
    title: 'إدارة المستخدمين',
    href: '/dashboard/users',
    icon: Users,
  },
  {
    title: 'الإعدادات',
    href: '/dashboard/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-gradient-to-b from-white to-gray-50 border-l border-gray-200 h-screen overflow-y-auto shadow-xl">
      {/* شعار الصفوة */}
      <div className="p-6 border-b border-gray-100">
        <Logo size="md" variant="full" />
      </div>

      <nav className="px-4 pb-4 mt-4">
        <ul className="space-y-1">
          {menuItems.map((item, index) => (
            <li key={index}>
              {item.children ? (
                <div>
                  <div className="flex items-center gap-3 px-4 py-3 text-sm font-semibold text-gray-600 bg-gray-50 rounded-xl mb-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-md">
                      <item.icon className="h-4 w-4 text-white" />
                    </div>
                    {item.title}
                  </div>
                  <ul className="mr-6 space-y-1">
                    {item.children.map((child, childIndex) => (
                      <li key={childIndex}>
                        <Link
                          href={child.href}
                          className={cn(
                            'block px-4 py-2.5 text-sm rounded-xl transition-all duration-200 font-medium',
                            pathname === child.href
                              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'
                              : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700 hover:transform hover:translate-x-1'
                          )}
                        >
                          <div className="flex items-center gap-2">
                            <div className={cn(
                              'w-2 h-2 rounded-full transition-all',
                              pathname === child.href ? 'bg-white' : 'bg-blue-300'
                            )} />
                            {child.title}
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <Link
                  href={item.href!}
                  className={cn(
                    'flex items-center gap-3 px-4 py-3 text-sm rounded-xl transition-all duration-200 font-medium mb-1',
                    pathname === item.href
                      ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700 hover:transform hover:translate-x-1'
                  )}
                >
                  <div className={cn(
                    'p-2 rounded-lg shadow-md transition-all',
                    pathname === item.href
                      ? 'bg-white/20'
                      : 'bg-gradient-to-br from-blue-500 to-blue-600'
                  )}>
                    <item.icon className={cn(
                      'h-4 w-4',
                      pathname === item.href ? 'text-white' : 'text-white'
                    )} />
                  </div>
                  {item.title}
                </Link>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </div>
  )
}
