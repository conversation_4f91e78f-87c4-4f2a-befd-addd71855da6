@echo off
echo.
echo 🔧 حل مشاكل التثبيت - نظام الصفوة للنسيج
echo ==========================================
echo.

echo [1/5] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js مثبت: !NODE_VERSION!
) else (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

echo.
echo [2/5] فحص npm باستخدام Command Prompt...
cmd /c "npm --version" >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('cmd /c "npm --version"') do set NPM_VERSION=%%i
    echo ✅ npm يعمل: !NPM_VERSION!
) else (
    echo ❌ npm لا يعمل
    pause
    exit /b 1
)

echo.
echo [3/5] فحص ملفات المشروع...
if exist "package.json" (
    echo ✅ ملف package.json موجود
) else (
    echo ❌ ملف package.json غير موجود
    echo تأكد من أنك في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo.
echo [4/5] تثبيت مكتبات المشروع...
echo هذا قد يستغرق بضع دقائق...
cmd /c "npm install"

if %errorlevel% equ 0 (
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ❌ فشل في تثبيت المكتبات
    echo جرب الحلول التالية:
    echo 1. cmd /c "npm cache clean --force"
    echo 2. cmd /c "npm install --legacy-peer-deps"
    pause
    exit /b 1
)

echo.
echo [5/5] إعداد ملف البيئة...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ تم إنشاء ملف .env
    ) else (
        echo ⚠️  ملف .env.example غير موجود
    )
) else (
    echo ℹ️  ملف .env موجود بالفعل
)

echo.
echo ==========================================
echo 🎉 تم إكمال التثبيت بنجاح!
echo ==========================================
echo.

echo 🚀 تشغيل النظام...
echo النظام سيعمل على: http://localhost:3000
echo.
echo 🔐 الحسابات التجريبية:
echo - مدير: <EMAIL> / admin123
echo - مشرف: <EMAIL> / super123
echo - مشغل: <EMAIL> / oper123
echo.
echo ⏹️  لإيقاف النظام: اضغط Ctrl+C
echo.

cmd /c "npm run dev"
