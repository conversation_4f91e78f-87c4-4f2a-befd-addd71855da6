'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDate, formatNumber } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, Play, Pause, CheckCircle } from 'lucide-react'

// بيانات تجريبية لأوامر العمل
const mockWorkOrders = [
  {
    id: '1',
    orderNumber: 'WO-2024-001',
    product: 'قميص قطني أزرق',
    productCode: 'PRD001',
    quantity: 100,
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-01-25'),
    assignedTo: 'مشغل الماكينة',
    progress: 65,
    notes: 'أولوية عالية - طلب عاجل من العميل',
  },
  {
    id: '2',
    orderNumber: 'WO-2024-002',
    product: 'بنطال جينز أزرق',
    productCode: 'PRD003',
    quantity: 50,
    status: 'PENDING',
    priority: 'MEDIUM',
    startDate: new Date('2024-01-20'),
    endDate: new Date('2024-01-30'),
    assignedTo: null,
    progress: 0,
    notes: 'في انتظار توفر المواد الخام',
  },
  {
    id: '3',
    orderNumber: 'WO-2024-003',
    product: 'فستان صيفي',
    productCode: 'PRD004',
    quantity: 75,
    status: 'COMPLETED',
    priority: 'LOW',
    startDate: new Date('2024-01-10'),
    endDate: new Date('2024-01-18'),
    assignedTo: 'مشغل الماكينة',
    progress: 100,
    notes: 'تم الانتهاء بنجاح',
  },
  {
    id: '4',
    orderNumber: 'WO-2024-004',
    product: 'قميص قطني أحمر',
    productCode: 'PRD002',
    quantity: 80,
    status: 'ON_HOLD',
    priority: 'URGENT',
    startDate: new Date('2024-01-22'),
    endDate: new Date('2024-01-28'),
    assignedTo: 'مشرف الإنتاج',
    progress: 25,
    notes: 'معلق بسبب عطل في الماكينة',
  },
]

const statusLabels = {
  PENDING: 'في الانتظار',
  IN_PROGRESS: 'قيد التنفيذ',
  COMPLETED: 'مكتمل',
  CANCELLED: 'ملغي',
  ON_HOLD: 'معلق',
}

const priorityLabels = {
  LOW: 'منخفضة',
  MEDIUM: 'متوسطة',
  HIGH: 'عالية',
  URGENT: 'عاجلة',
}

export function WorkOrdersList() {
  const [workOrders] = useState(mockWorkOrders)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'ON_HOLD':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW':
        return 'bg-gray-100 text-gray-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'HIGH':
        return 'bg-orange-100 text-orange-800'
      case 'URGENT':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500'
    if (progress >= 75) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    if (progress >= 25) return 'bg-orange-500'
    return 'bg-gray-300'
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم الأمر</TableHead>
            <TableHead>المنتج</TableHead>
            <TableHead>الكمية</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead>الأولوية</TableHead>
            <TableHead>التقدم</TableHead>
            <TableHead>تاريخ البدء</TableHead>
            <TableHead>تاريخ الانتهاء</TableHead>
            <TableHead>المسؤول</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {workOrders.map((order) => (
            <TableRow key={order.id}>
              <TableCell className="font-medium">
                {order.orderNumber}
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{order.product}</div>
                  <div className="text-sm text-muted-foreground">
                    {order.productCode}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {formatNumber(order.quantity)} قطعة
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(order.status)}>
                  {statusLabels[order.status as keyof typeof statusLabels]}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge className={getPriorityColor(order.priority)}>
                  {priorityLabels[order.priority as keyof typeof priorityLabels]}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${getProgressColor(order.progress)}`}
                      style={{ width: `${order.progress}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium">{order.progress}%</span>
                </div>
              </TableCell>
              <TableCell>
                {formatDate(order.startDate)}
              </TableCell>
              <TableCell>
                {formatDate(order.endDate)}
              </TableCell>
              <TableCell>
                <div className="text-sm">
                  {order.assignedTo || 'غير محدد'}
                </div>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Eye className="ml-2 h-4 w-4" />
                      عرض التفاصيل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </DropdownMenuItem>
                    {order.status === 'PENDING' && (
                      <DropdownMenuItem>
                        <Play className="ml-2 h-4 w-4" />
                        بدء التنفيذ
                      </DropdownMenuItem>
                    )}
                    {order.status === 'IN_PROGRESS' && (
                      <>
                        <DropdownMenuItem>
                          <Pause className="ml-2 h-4 w-4" />
                          إيقاف مؤقت
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <CheckCircle className="ml-2 h-4 w-4" />
                          إكمال
                        </DropdownMenuItem>
                      </>
                    )}
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="ml-2 h-4 w-4" />
                      حذف
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
