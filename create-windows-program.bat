@echo off
title إنشاء برنامج Windows - نظام الصفوة للنسيج
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء برنامج Windows حقيقي - نظام الصفوة للنسيج
echo                              🖥️ مثل أي برنامج Windows عادي 🖥️
echo.
echo ===============================================================================
echo.

echo مرحباً بك في منشئ برنامج نظام الصفوة للنسيج!
echo.
echo هذا المنشئ سيحول النظام إلى برنامج Windows حقيقي:
echo ✓ يعمل بدون Node.js
echo ✓ يعمل بدون إنترنت
echo ✓ مثبت احترافي مثل أي برنامج
echo ✓ اختصارات في سطح المكتب وقائمة ابدأ
echo ✓ إلغاء تثبيت من لوحة التحكم
echo ✓ جميع وحدات النسيج مدمجة
echo.
echo اختر نوع البرنامج:
echo.
echo [1] برنامج كامل مع مثبت (موصى به)
echo [2] برنامج محمول بدون تثبيت
echo [3] كلاهما
echo [4] إلغاء
echo.
set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto full_program
if "%choice%"=="2" goto portable_program
if "%choice%"=="3" goto both_programs
if "%choice%"=="4" goto cancel
echo خيار غير صحيح!
pause
exit /b 1

:full_program
echo.
echo ===============================================
echo إنشاء برنامج كامل مع مثبت
echo ===============================================
echo.
call build-real-program.bat
goto success

:portable_program
echo.
echo ===============================================
echo إنشاء برنامج محمول
echo ===============================================
echo.
echo [1/8] تحضير البيئة...
set NODE_ENV=production

echo [2/8] بناء الملفات الثابتة...
npm run build 2>nul

echo [3/8] تثبيت Electron...
npm install electron --save-dev

echo [4/8] إنشاء برنامج محمول...
npx electron-packager . "نظام الصفوة للنسيج" --platform=win32 --arch=x64 --out=portable --overwrite --icon=build/icon.ico

echo [5/8] نسخ الملفات المطلوبة...
if exist "portable" (
    xcopy "data" "portable\نظام الصفوة للنسيج-win32-x64\data" /E /I /Q 2>nul
    xcopy "out" "portable\نظام الصفوة للنسيج-win32-x64\out" /E /I /Q 2>nul
)

echo [6/8] إنشاء ملف تشغيل...
if exist "portable\نظام الصفوة للنسيج-win32-x64" (
    (
    echo @echo off
    echo title نظام الصفوة للنسيج
    echo cd /d "%%~dp0"
    echo start "" "نظام الصفوة للنسيج.exe"
    ) > "portable\نظام الصفوة للنسيج-win32-x64\تشغيل النظام.bat"
)

echo [7/8] ضغط البرنامج المحمول...
if exist "portable\نظام الصفوة للنسيج-win32-x64" (
    powershell -Command "Compress-Archive -Path 'portable\نظام الصفوة للنسيج-win32-x64' -DestinationPath 'Safa-Textile-Program-Portable.zip' -Force"
)

echo [8/8] التحقق من النتيجة...
if exist "Safa-Textile-Program-Portable.zip" (
    echo ✓ تم إنشاء البرنامج المحمول بنجاح!
) else (
    echo ✗ فشل في إنشاء البرنامج المحمول
)
goto success

:both_programs
echo.
echo ===============================================
echo إنشاء كلا النوعين
echo ===============================================
echo.
echo [1/2] إنشاء البرنامج الكامل...
call build-real-program.bat
echo.
echo [2/2] إنشاء البرنامج المحمول...
goto portable_program

:success
echo.
echo ===============================================
echo ✓ تم إكمال إنشاء البرنامج بنجاح!
echo ===============================================
echo.

if exist "dist" (
    echo 🖥️ البرنامج الكامل مع المثبت:
    for /r dist %%i in (*.exe) do (
        echo    ملف: %%~nxi
        echo    الحجم: %%~zi bytes
        echo    النوع: مثبت Windows احترافي
        echo    المسار: %%i
    )
    echo.
    echo    المميزات:
    echo    ✓ يثبت مثل أي برنامج Windows
    echo    ✓ اختصارات تلقائية
    echo    ✓ إلغاء تثبيت من لوحة التحكم
    echo    ✓ يعمل بدون أي متطلبات
    echo.
)

if exist "Safa-Textile-Program-Portable.zip" (
    echo 🎒 البرنامج المحمول:
    for %%i in (Safa-Textile-Program-Portable.zip) do (
        echo    ملف: %%~nxi
        echo    الحجم: %%~zi bytes
        echo    النوع: برنامج محمول
    )
    echo.
    echo    المميزات:
    echo    ✓ يعمل من أي مجلد
    echo    ✓ لا يحتاج تثبيت
    echo    ✓ سهل النقل والتوزيع
    echo    ✓ يعمل بدون أي متطلبات
    echo.
)

echo ===============================================
echo طرق الاستخدام:
echo ===============================================
echo.
echo 🏆 للاستخدام الاحترافي:
if exist "dist" (
    echo    1. شغل ملف المثبت من مجلد dist
    echo    2. اتبع تعليمات التثبيت
    echo    3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
    echo    4. أو انقر على الاختصار في سطح المكتب
)

echo.
echo 🎒 للاستخدام المحمول:
if exist "Safa-Textile-Program-Portable.zip" (
    echo    1. فك ضغط ملف Safa-Textile-Program-Portable.zip
    echo    2. شغل "تشغيل النظام.bat"
    echo    3. أو شغل "نظام الصفوة للنسيج.exe" مباشرة
)

echo.
echo 🔐 تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo ===============================================
echo معلومات البرنامج
echo ===============================================
echo.
echo 🏭 الوحدات المتاحة:
echo    • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo    • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo    • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo    • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo    • المالية: الحسابات، الفواتير، التقارير المالية
echo    • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo    • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo ✨ المميزات:
echo    ✓ يعمل مثل أي برنامج Windows عادي
echo    ✓ لا يحتاج Node.js أو أي متطلبات
echo    ✓ يعمل بدون إنترنت تماماً
echo    ✓ واجهة عربية احترافية
echo    ✓ شعار الصفوة في جميع الصفحات
echo    ✓ بيانات تجريبية واقعية
echo    ✓ أداء فائق وسرعة عالية
echo.
echo 💻 متطلبات النظام:
echo    - Windows 10/11 (أي إصدار)
echo    - 4GB RAM (الحد الأدنى)
echo    - 2GB مساحة قرص
echo    - لا يحتاج أي برامج إضافية
echo.
echo ===============================================
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo ===============================================
goto end

:cancel
echo تم إلغاء إنشاء البرنامج.
goto end

:end
echo.
pause
