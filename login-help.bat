@echo off
echo.
echo Safa Textile ERP - Login Help
echo ==============================
echo.

echo [1/3] Checking system status...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %errorlevel% equ 0 (
    echo System is running
) else (
    echo System is not running
    echo Starting system...
    start cmd /c "cd /d %~dp0 && npm run dev"
    timeout /t 10 /nobreak >nul
)

echo.
echo [2/3] Testing connection...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo System available at: http://localhost:3000
    set URL=http://localhost:3000
) else (
    curl -s http://localhost:3001 >nul 2>&1
    if %errorlevel% equ 0 (
        echo System available at: http://localhost:3001
        set URL=http://localhost:3001
    ) else (
        echo System not responding, restarting...
        taskkill /F /IM node.exe 2>nul
        start cmd /c "cd /d %~dp0 && npm run dev"
        timeout /t 15 /nobreak >nul
        set URL=http://localhost:3000
    )
)

echo.
echo [3/3] Opening browser...
if defined URL (
    start %URL%
) else (
    start http://localhost:3000
)

echo.
echo ==============================
echo LOGIN CREDENTIALS:
echo ==============================
echo.
echo ADMIN (Full Access):
echo Email: <EMAIL>
echo Password: admin123
echo.
echo SUPERVISOR:
echo Email: <EMAIL>
echo Password: super123
echo.
echo OPERATOR:
echo Email: <EMAIL>
echo Password: oper123
echo.
echo ==============================
echo TROUBLESHOOTING TIPS:
echo ==============================
echo.
echo 1. Copy and paste the email exactly
echo 2. Password is case-sensitive
echo 3. Make sure no extra spaces
echo 4. Refresh page (F5) if needed
echo 5. Try different browser if issues persist
echo.
echo If login still fails:
echo - Run this file again
echo - Restart your browser
echo - Clear browser cache
echo.
pause
