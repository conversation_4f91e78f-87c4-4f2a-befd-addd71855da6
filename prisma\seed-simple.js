const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 إضافة البيانات التجريبية...')

  // حذف البيانات الموجودة
  await prisma.user.deleteMany({})

  // إنشاء كلمات مرور مشفرة
  const hashedAdminPassword = await bcrypt.hash('admin123', 12)
  const hashedSuperPassword = await bcrypt.hash('super123', 12)
  const hashedOperPassword = await bcrypt.hash('oper123', 12)

  // إنشاء المستخدمين
  const admin = await prisma.user.create({
    data: {
      username: 'admin',
      email: '<EMAIL>',
      name: 'مدير النظام',
      password: hashedAdminPassword,
      role: 'ADMIN',
      department: 'الإدارة العامة',
      isActive: true,
    },
  })

  const supervisor = await prisma.user.create({
    data: {
      username: 'supervisor',
      email: '<EMAIL>',
      name: 'مشرف الإنتاج',
      password: hashedSuperPassword,
      role: 'SUPERVISOR',
      department: 'الإنتاج',
      isActive: true,
    },
  })

  const operator = await prisma.user.create({
    data: {
      username: 'operator',
      email: '<EMAIL>',
      name: 'مشغل الماكينة',
      password: hashedOperPassword,
      role: 'OPERATOR',
      department: 'الإنتاج',
      isActive: true,
    },
  })

  console.log('✅ تم إنشاء المستخدمين بنجاح!')
  console.log(`👤 ${admin.name} - اسم المستخدم: ${admin.username}`)
  console.log(`👤 ${supervisor.name} - اسم المستخدم: ${supervisor.username}`)
  console.log(`👤 ${operator.name} - اسم المستخدم: ${operator.username}`)
  
  console.log('')
  console.log('🔐 بيانات تسجيل الدخول:')
  console.log('- مدير النظام: admin / admin123')
  console.log('- مشرف الإنتاج: supervisor / super123')
  console.log('- مشغل الماكينة: operator / oper123')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ خطأ في إنشاء البيانات:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
