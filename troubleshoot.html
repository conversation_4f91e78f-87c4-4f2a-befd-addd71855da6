<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استكشاف الأخطاء - نظام الصفوة للنسيج</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        
        .problem-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .problem-title {
            color: #92400e;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .problem-desc {
            color: #78350f;
            line-height: 1.6;
        }
        
        .solution-section {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .solution-title {
            color: #065f46;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-right: 4px solid #10b981;
        }
        
        .step-number {
            display: inline-block;
            background: #10b981;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: 600;
            margin-left: 10px;
            font-size: 14px;
        }
        
        .step-text {
            color: #047857;
            font-weight: 500;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            border: 1px solid #374151;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid #ef4444;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning-text {
            color: #991b1b;
            font-weight: 600;
        }
        
        .info-box {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .info-text {
            color: #1e40af;
            font-weight: 500;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.2s ease;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .final-section {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-top: 30px;
        }
        
        .final-title {
            color: #5b21b6;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
        }
        
        .final-text {
            color: #6b21a8;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔧 استكشاف الأخطاء</div>
            <div class="subtitle">نظام الصفوة للنسيج - حل مشاكل التثبيت</div>
        </div>
        
        <!-- Problem Identification -->
        <div class="problem-section">
            <div class="problem-title">
                ⚠️ المشكلة المحددة
            </div>
            <div class="problem-desc">
                Node.js مثبت ولكن غير متاح في Command Prompt. هذا يحدث عادة لأن متغيرات البيئة (PATH) لم يتم تحديثها بعد.
            </div>
        </div>
        
        <!-- Solution 1: Restart Command Prompt -->
        <div class="solution-section">
            <div class="solution-title">
                🔄 الحل الأول: إعادة تشغيل Command Prompt
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <span class="step-text">أغلق جميع نوافذ Command Prompt المفتوحة</span>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <span class="step-text">اضغط Win + R واكتب "cmd" ثم اضغط Enter</span>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <span class="step-text">انتقل لمجلد المشروع واختبر Node.js:</span>
            </div>
            
            <div class="code-block">
cd "C:\Users\<USER>\Desktop\الصفوه\1003"<br>
node --version
            </div>
        </div>
        
        <!-- Solution 2: Restart Computer -->
        <div class="solution-section">
            <div class="solution-title">
                🔄 الحل الثاني: إعادة تشغيل الكمبيوتر
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <span class="step-text">احفظ جميع أعمالك</span>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <span class="step-text">أعد تشغيل الكمبيوتر</span>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <span class="step-text">بعد إعادة التشغيل، افتح Command Prompt واختبر:</span>
            </div>
            
            <div class="code-block">
node --version<br>
npm --version
            </div>
        </div>
        
        <!-- Solution 3: Manual PATH -->
        <div class="solution-section">
            <div class="solution-title">
                ⚙️ الحل الثالث: إضافة Node.js للـ PATH يدوياً
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <span class="step-text">اضغط Win + X واختر "System"</span>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <span class="step-text">اضغط على "Advanced system settings"</span>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <span class="step-text">اضغط على "Environment Variables"</span>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <span class="step-text">في "System variables"، ابحث عن "Path" واضغط "Edit"</span>
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <span class="step-text">اضغط "New" وأضف:</span>
            </div>
            
            <div class="code-block">
C:\Program Files\nodejs\
            </div>
            
            <div class="warning-box">
                <div class="warning-text">
                    ⚠️ تأكد من المسار الصحيح لـ Node.js في جهازك
                </div>
            </div>
        </div>
        
        <!-- Alternative: Direct Installation Check -->
        <div class="info-box">
            <div class="info-text">
                💡 <strong>للتحقق من التثبيت:</strong> ابحث عن "Node.js" في قائمة Start. إذا وجدته، فهو مثبت بنجاح.
            </div>
        </div>
        
        <!-- Quick Test -->
        <div class="solution-section">
            <div class="solution-title">
                🧪 اختبار سريع
            </div>
            
            <div class="step">
                <span class="step-number">1</span>
                <span class="step-text">بعد تطبيق أي من الحلول أعلاه، شغل:</span>
            </div>
            
            <div class="code-block">
simple-check.bat
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <span class="step-text">إذا ظهر "Node.js: Ready"، شغل النظام:</span>
            </div>
            
            <div class="code-block">
run-system.bat
            </div>
        </div>
        
        <!-- Download Links -->
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://nodejs.org/" class="btn" target="_blank">
                📥 إعادة تحميل Node.js
            </a>
            <a href="https://www.postgresql.org/download/windows/" class="btn" target="_blank">
                📥 تحميل PostgreSQL
            </a>
        </div>
        
        <!-- Final Instructions -->
        <div class="final-section">
            <div class="final-title">🎯 بعد حل المشكلة</div>
            <div class="final-text">
                عندما يعمل Node.js بنجاح، ستتمكن من تشغيل النظام والوصول إليه على:<br>
                <strong>http://localhost:3000</strong><br><br>
                
                🔐 الحسابات التجريبية:<br>
                • مدير: <EMAIL> / admin123<br>
                • مشرف: <EMAIL> / super123<br>
                • مشغل: <EMAIL> / oper123
            </div>
        </div>
    </div>
</body>
</html>
