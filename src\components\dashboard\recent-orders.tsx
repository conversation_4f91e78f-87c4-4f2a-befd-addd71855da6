import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'

const recentOrders = [
  {
    id: 'WO-2024-001',
    product: 'قميص قطني أزرق',
    quantity: 100,
    status: 'IN_PROGRESS',
    statusText: 'قيد التنفيذ',
    priority: 'HIGH',
    priorityText: 'عالية',
    startDate: new Date('2024-01-15'),
  },
  {
    id: 'WO-2024-002',
    product: 'بنطال جينز',
    quantity: 50,
    status: 'PENDING',
    statusText: 'في الانتظار',
    priority: 'MEDIUM',
    priorityText: 'متوسطة',
    startDate: new Date('2024-01-16'),
  },
  {
    id: 'WO-2024-003',
    product: 'فستان صيفي',
    quantity: 75,
    status: 'COMPLETED',
    statusText: 'مكتمل',
    priority: 'LOW',
    priorityText: 'منخفضة',
    startDate: new Date('2024-01-14'),
  },
]

export function RecentOrders() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800'
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800'
      case 'LOW':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>أوامر العمل الحديثة</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentOrders.map((order) => (
            <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{order.id}</span>
                  <Badge className={getPriorityColor(order.priority)}>
                    {order.priorityText}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{order.product}</p>
                <p className="text-xs text-muted-foreground">
                  الكمية: {order.quantity} قطعة
                </p>
                <p className="text-xs text-muted-foreground">
                  تاريخ البدء: {formatDate(order.startDate)}
                </p>
              </div>
              <Badge className={getStatusColor(order.status)}>
                {order.statusText}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
