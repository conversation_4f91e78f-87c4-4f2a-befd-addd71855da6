@echo off
chcp 65001 >nul
echo.
echo 🖥️  نظام الصفوة للنسيج - الوضع المحلي
echo ========================================
echo.

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)

echo ✅ Node.js متاح

REM التحقق من قاعدة البيانات المحلية
if not exist "data\database.db" (
    echo 📊 إعداد قاعدة البيانات المحلية...
    if not exist "data" mkdir data
    copy "next.config.offline.js" "next.config.js" >nul
    cmd /c "npx prisma db push"
    cmd /c "npx prisma db seed"
    echo ✅ تم إعداد قاعدة البيانات المحلية
) else (
    echo ✅ قاعدة البيانات المحلية جاهزة
)

REM نسخ إعدادات الوضع المحلي
copy "next.config.offline.js" "next.config.js" >nul
if exist ".env.offline" (
    copy ".env.offline" ".env" >nul
) else (
    echo DATABASE_URL="file:./data/database.db" > .env
    echo NEXTAUTH_URL="http://localhost:3000" >> .env
    echo NEXTAUTH_SECRET="safa-textile-offline-2024" >> .env
    echo OFFLINE_MODE="true" >> .env
)

echo.
echo 🔒 الوضع: محلي بدون إنترنت
echo 📊 قاعدة البيانات: SQLite
echo 🌐 العنوان: http://localhost:3000
echo.

REM التحقق من المكتبات
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات...
    cmd /c "npm install"
)

REM بناء النظام للإنتاج
echo 🔨 بناء النظام...
cmd /c "npm run build"

if %errorlevel% neq 0 (
    echo ⚠️  فشل في البناء، تشغيل وضع التطوير...
    cmd /c "npm run dev"
) else (
    echo ✅ تم بناء النظام بنجاح
    echo.
    echo 🚀 تشغيل النظام في الوضع المحلي...
    echo.
    echo =======================================
    echo 🌐 النظام متاح على: http://localhost:3000
    echo =======================================
    echo.
    echo 🔐 الحسابات التجريبية:
    echo - مدير: <EMAIL> / admin123
    echo - مشرف: <EMAIL> / super123
    echo - مشغل: <EMAIL> / oper123
    echo.
    echo 💾 البيانات محفوظة محلياً في: data\database.db
    echo 🔒 النظام يعمل بدون إنترنت
    echo ⏹️  لإيقاف النظام: اضغط Ctrl+C
    echo.
    
    cmd /c "npm start"
)
