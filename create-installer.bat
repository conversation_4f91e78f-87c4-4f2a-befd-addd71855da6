@echo off
title إنشاء مثبت نظام الصفوة للنسيج
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء مثبت نظام الصفوة للنسيج
echo                              Windows Installer Creator
echo.
echo ===============================================================================
echo.

echo [1/12] إيقاف العمليات السابقة...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM electron.exe 2>nul

echo [2/12] تحضير البيئة...
set NODE_ENV=production
set ELECTRON_BUILDER_CACHE_DIR=%TEMP%\electron-builder

echo [3/12] تثبيت أدوات البناء...
echo تثبيت Electron Builder...
npm install electron-builder@latest --save-dev
npm install electron@latest --save-dev

echo [4/12] إنشاء مجلد البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"

echo [5/12] نسخ الأيقونات...
if not exist "build\icon.ico" (
    echo إنشاء أيقونة افتراضية...
    copy "public\favicon.ico" "build\icon.ico" 2>nul
)

echo [6/12] تحضير ملف package.json للبناء...
if exist package.json copy package.json package.json.backup

echo إنشاء package.json محسن للبناء...
(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لإدارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": "شركة الصفوة للنسيج",
echo   "scripts": {
echo     "electron": "electron .",
echo     "build-electron": "electron-builder",
echo     "dist": "electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "src/**/*",
echo       "public/**/*",
echo       "electron/**/*",
echo       "data/**/*",
echo       "node_modules/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico",
echo       "requestedExecutionLevel": "asInvoker"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج",
echo       "installerIcon": "build/icon.ico",
echo       "uninstallerIcon": "build/icon.ico",
echo       "installerHeaderIcon": "build/icon.ico",
echo       "displayLanguageSelector": false,
echo       "language": "1025"
echo     }
echo   }
echo }
) > package.json

echo [7/12] تحضير البيانات...
if not exist "data" mkdir "data"

echo [8/12] بناء التطبيق...
echo جاري بناء نظام الصفوة للنسيج...
npm run build 2>nul

echo [9/12] إنشاء ملف التشغيل المدمج...
(
echo @echo off
echo title نظام الصفوة للنسيج
echo cd /d "%%~dp0"
echo start "" "نظام الصفوة للنسيج.exe"
) > "start-safa-textile.bat"

echo [10/12] بناء المثبت...
echo إنشاء مثبت Windows...
npm run dist

echo [11/12] التحقق من النتيجة...
if exist "dist" (
    echo.
    echo ===============================================
    echo ✓ تم إنشاء المثبت بنجاح!
    echo ===============================================
    echo.
    echo الملفات المُنشأة:
    dir dist /b
    echo.
    echo تفاصيل المثبت:
    for /r dist %%i in (*.exe) do (
        echo اسم الملف: %%~nxi
        echo الحجم: %%~zi bytes
        echo المسار: %%i
        echo.
    )
    echo.
    echo المميزات:
    echo ✓ مثبت Windows احترافي
    echo ✓ اختصارات سطح المكتب وقائمة ابدأ
    echo ✓ إمكانية اختيار مجلد التثبيت
    echo ✓ إلغاء تثبيت آمن
    echo ✓ يعمل بدون إنترنت
    echo ✓ جميع وحدات النسيج مدمجة
    echo.
) else (
    echo ✗ فشل في إنشاء المثبت
    echo.
    echo الأسباب المحتملة:
    echo - نقص في مساحة القرص
    echo - مكافح الفيروسات يحجب العملية
    echo - نقص في الصلاحيات
    echo - خطأ في إعدادات البناء
    echo.
    echo الحلول:
    echo 1. شغل Command Prompt كمدير
    echo 2. أغلق مكافح الفيروسات مؤقتاً
    echo 3. تأكد من مساحة القرص الكافية
    echo 4. أعد المحاولة
)

echo [12/12] تنظيف الملفات المؤقتة...
if exist package.json.backup (
    copy package.json.backup package.json
    del package.json.backup
)

echo.
echo ===============================================
echo اكتمل إنشاء المثبت!
echo ===============================================
echo.
echo يمكنك الآن:
echo 1. توزيع ملف المثبت على أي جهاز Windows
echo 2. تثبيت النظام بدون إنترنت
echo 3. تشغيل النظام من قائمة ابدأ أو سطح المكتب
echo.
pause
