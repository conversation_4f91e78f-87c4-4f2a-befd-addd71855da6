<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تسجيل الدخول - نظام الصفوة للنسيج</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        
        .account-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-right: 5px solid #2563eb;
        }
        
        .account-title {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .account-details {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .email, .password {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .label {
            font-weight: 600;
            color: #374151;
        }
        
        .value {
            background: #f3f4f6;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            color: #1f2937;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .value:hover {
            background: #e5e7eb;
        }
        
        .copy-btn {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .copy-btn:hover {
            transform: translateY(-1px);
        }
        
        .permissions {
            font-size: 14px;
            color: #6b7280;
            margin-top: 10px;
        }
        
        .steps-section {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .steps-title {
            color: #065f46;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-number {
            background: #10b981;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }
        
        .step-text {
            color: #047857;
            font-weight: 500;
        }
        
        .troubleshoot-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .troubleshoot-title {
            color: #92400e;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .tip {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            color: #78350f;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: transform 0.2s ease;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .status-section {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .status-title {
            color: #1e40af;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .status-text {
            color: #1e3a8a;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 دليل تسجيل الدخول</div>
            <div class="subtitle">نظام الصفوة للنسيج - إدارة موارد المؤسسة</div>
        </div>
        
        <!-- System Status -->
        <div class="status-section">
            <div class="status-title">✅ حالة النظام</div>
            <div class="status-text">
                النظام يعمل بنجاح على: <strong>http://localhost:3000</strong>
            </div>
        </div>
        
        <!-- Login Steps -->
        <div class="steps-section">
            <div class="steps-title">📋 خطوات تسجيل الدخول</div>
            
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">افتح المتصفح واذهب إلى: http://localhost:3000</div>
            </div>
            
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">اختر أحد الحسابات أدناه</div>
            </div>
            
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">انسخ البريد الإلكتروني والصقه في الحقل</div>
            </div>
            
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-text">انسخ كلمة المرور والصقها في الحقل</div>
            </div>
            
            <div class="step">
                <div class="step-number">5</div>
                <div class="step-text">اضغط "تسجيل الدخول"</div>
            </div>
        </div>
        
        <!-- Admin Account -->
        <div class="account-section">
            <div class="account-title">
                👑 مدير النظام (صلاحيات كاملة)
            </div>
            <div class="account-details">
                <div class="email">
                    <span class="label">البريد الإلكتروني:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('<EMAIL>')">نسخ</button>
                        <span class="value" onclick="copyText('<EMAIL>')"><EMAIL></span>
                    </div>
                </div>
                <div class="password">
                    <span class="label">كلمة المرور:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('admin123')">نسخ</button>
                        <span class="value" onclick="copyText('admin123')">admin123</span>
                    </div>
                </div>
                <div class="permissions">
                    🔓 الصلاحيات: الوصول لجميع الوحدات والإعدادات
                </div>
            </div>
        </div>
        
        <!-- Supervisor Account -->
        <div class="account-section">
            <div class="account-title">
                👨‍💼 مشرف الإنتاج
            </div>
            <div class="account-details">
                <div class="email">
                    <span class="label">البريد الإلكتروني:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('<EMAIL>')">نسخ</button>
                        <span class="value" onclick="copyText('<EMAIL>')"><EMAIL></span>
                    </div>
                </div>
                <div class="password">
                    <span class="label">كلمة المرور:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('super123')">نسخ</button>
                        <span class="value" onclick="copyText('super123')">super123</span>
                    </div>
                </div>
                <div class="permissions">
                    🔒 الصلاحيات: الإنتاج، الجودة، المخزون
                </div>
            </div>
        </div>
        
        <!-- Operator Account -->
        <div class="account-section">
            <div class="account-title">
                👷‍♂️ مشغل الماكينة
            </div>
            <div class="account-details">
                <div class="email">
                    <span class="label">البريد الإلكتروني:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('<EMAIL>')">نسخ</button>
                        <span class="value" onclick="copyText('<EMAIL>')"><EMAIL></span>
                    </div>
                </div>
                <div class="password">
                    <span class="label">كلمة المرور:</span>
                    <div>
                        <button class="copy-btn" onclick="copyText('oper123')">نسخ</button>
                        <span class="value" onclick="copyText('oper123')">oper123</span>
                    </div>
                </div>
                <div class="permissions">
                    🔐 الصلاحيات: تسجيل الإنتاج ومراقبة الماكينات
                </div>
            </div>
        </div>
        
        <!-- Troubleshooting -->
        <div class="troubleshoot-section">
            <div class="troubleshoot-title">🔧 حل المشاكل</div>
            
            <div class="tip">
                <strong>إذا لم تعمل البيانات:</strong> تأكد من نسخ البريد الإلكتروني وكلمة المرور بالكامل
            </div>
            
            <div class="tip">
                <strong>إذا ظهرت رسالة خطأ:</strong> أعد تحميل الصفحة (F5) وحاول مرة أخرى
            </div>
            
            <div class="tip">
                <strong>إذا لم تفتح الصفحة:</strong> تأكد من أن النظام يعمل وشغل login-help.bat
            </div>
            
            <div class="tip">
                <strong>للمساعدة الإضافية:</strong> شغل ملف login-help.bat من مجلد المشروع
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div style="text-align: center;">
            <a href="http://localhost:3000" class="btn" target="_blank">
                🌐 فتح النظام
            </a>
            <button class="btn btn-success" onclick="copyAllAdmin()">
                📋 نسخ بيانات المدير
            </button>
        </div>
    </div>
    
    <script>
        function copyText(text) {
            navigator.clipboard.writeText(text).then(function() {
                // إظهار رسالة نجاح
                const notification = document.createElement('div');
                notification.textContent = 'تم النسخ: ' + text;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    z-index: 1000;
                    font-family: Cairo, sans-serif;
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
            });
        }
        
        function copyAllAdmin() {
            const adminData = `البريد: <EMAIL>
كلمة المرور: admin123`;
            copyText(adminData);
        }
    </script>
</body>
</html>
