'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Calendar } from 'lucide-react'

const workOrderSchema = z.object({
  orderNumber: z.string().min(1, 'رقم الأمر مطلوب'),
  productId: z.string().min(1, 'المنتج مطلوب'),
  quantity: z.number().min(1, 'الكمية يجب أن تكون أكبر من 0'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  startDate: z.string().min(1, 'تاريخ البدء مطلوب'),
  endDate: z.string().min(1, 'تاريخ الانتهاء مطلوب'),
  assignedToId: z.string().optional(),
  notes: z.string().optional(),
})

type WorkOrderFormData = z.infer<typeof workOrderSchema>

interface AddWorkOrderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// بيانات تجريبية للمنتجات
const mockProducts = [
  { id: '1', code: 'PRD001', name: 'قميص قطني أزرق' },
  { id: '2', code: 'PRD002', name: 'قميص قطني أحمر' },
  { id: '3', code: 'PRD003', name: 'بنطال جينز أزرق' },
  { id: '4', code: 'PRD004', name: 'فستان صيفي' },
]

// بيانات تجريبية للموظفين
const mockEmployees = [
  { id: '1', name: 'مشغل الماكينة' },
  { id: '2', name: 'مشرف الإنتاج' },
  { id: '3', name: 'فني الصيانة' },
]

export function AddWorkOrderDialog({ open, onOpenChange }: AddWorkOrderDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<WorkOrderFormData>({
    resolver: zodResolver(workOrderSchema),
    defaultValues: {
      orderNumber: '',
      productId: '',
      quantity: 1,
      priority: 'MEDIUM',
      startDate: '',
      endDate: '',
      assignedToId: '',
      notes: '',
    },
  })

  const onSubmit = async (data: WorkOrderFormData) => {
    setIsLoading(true)
    
    try {
      // هنا سيتم إرسال البيانات إلى API
      console.log('Work Order data:', data)
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'تم إنشاء أمر العمل بنجاح',
        description: `تم إنشاء أمر العمل ${data.orderNumber}`,
      })
      
      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'خطأ في إنشاء أمر العمل',
        description: 'حدث خطأ أثناء إنشاء أمر العمل. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // توليد رقم أمر عمل تلقائي
  const generateOrderNumber = () => {
    const timestamp = Date.now().toString().slice(-6)
    const orderNumber = `WO-2024-${timestamp}`
    form.setValue('orderNumber', orderNumber)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>إنشاء أمر عمل جديد</DialogTitle>
          <DialogDescription>
            أدخل تفاصيل أمر العمل الجديد لبدء عملية الإنتاج
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="orderNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رقم أمر العمل *</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input placeholder="WO-2024-001" {...field} />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateOrderNumber}
                        size="sm"
                      >
                        توليد
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الأولوية *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الأولوية" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="LOW">منخفضة</SelectItem>
                        <SelectItem value="MEDIUM">متوسطة</SelectItem>
                        <SelectItem value="HIGH">عالية</SelectItem>
                        <SelectItem value="URGENT">عاجلة</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>المنتج *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر المنتج" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockProducts.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.code} - {product.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الكمية المطلوبة *</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="100"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>تاريخ البدء *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="date"
                          {...field}
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>تاريخ الانتهاء المتوقع *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="date"
                          {...field}
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="assignedToId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>المسؤول عن التنفيذ</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر المسؤول" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {mockEmployees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ملاحظات</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="ملاحظات إضافية حول أمر العمل..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري الإنشاء...
                  </>
                ) : (
                  'إنشاء أمر العمل'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
