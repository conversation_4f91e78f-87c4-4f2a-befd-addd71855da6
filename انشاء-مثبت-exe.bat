@echo off
title انشاء مثبت نظام الصفوة EXE - الطريقة الاسهل
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           انشاء مثبت نظام الصفوة EXE - الطريقة الاسهل
echo                              📦 ملف EXE واحد بنقرة واحدة 📦
echo.
echo ===============================================================================
echo.

echo مرحباً! هذا هو اسهل طريق لانشاء مثبت EXE لنظام الصفوة
echo.
echo ما سيحدث:
echo ✓ انشاء ملف EXE واحد شامل
echo ✓ يعمل على اي جهاز Windows
echo ✓ لا يحتاج اي برامج اضافية
echo ✓ تثبيت تلقائي بنقرة واحدة
echo.

echo [1/5] انشاء مجلد المثبت...
set INSTALLER_NAME=نظام-الصفوة-مثبت-EXE
if exist "%INSTALLER_NAME%" rmdir /s /q "%INSTALLER_NAME%"
mkdir "%INSTALLER_NAME%"

echo [2/5] انشاء ملف التثبيت الرئيسي...
(
echo @echo off
echo title مثبت نظام الصفوة للنسيج
echo color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           مثبت نظام الصفوة للنسيج
echo                              📦 مثبت EXE شامل 📦
echo.
echo ===============================================================================
echo.
echo مرحباً بك في مثبت نظام الصفوة لادارة مصانع النسيج!
echo.
echo معلومات النظام:
echo • الاسم: نظام الصفوة لادارة مصانع النسيج
echo • الاصدار: 1.0.0
echo • المطور: شركة الصفوة للنسيج
echo • النوع: نظام ERP متكامل
echo.
echo الوحدات المتاحة:
echo ✓ الانتاج: مراحل الانتاج، تخطيط الانتاج، اوامر العمل
echo ✓ المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo ✓ المشتريات: طلبات الشراء، اوامر الشراء، الموردين
echo ✓ المبيعات: عروض الاسعار، اوامر البيع، العملاء
echo ✓ المالية: الحسابات، الفواتير، التقارير المالية
echo ✓ الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo ✓ اخرى: الجودة، الصيانة، ادارة المستخدمين، الاعدادات
echo.
echo المميزات:
echo ✓ يعمل بدون انترنت تماماً
echo ✓ واجهة عربية احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ بيانات تجريبية واقعية
echo ✓ اداء فائق وسرعة عالية
echo.
echo هل تريد المتابعة مع التثبيت؟ ^(Y/N^)
set /p confirm="اكتب Y للمتابعة او N للالغاء: "
echo.
if /i "%%confirm%%" neq "Y" ^(
    echo تم الغاء التثبيت.
    pause
    exit /b 0
^)
echo.
echo ===============================================
echo بدء تثبيت نظام الصفوة للنسيج
echo ===============================================
echo.
echo [1/10] فحص متطلبات النظام...
echo فحص نظام التشغيل...
ver ^| find "Windows" ^>nul
if errorlevel 1 ^(
    echo ✗ هذا البرنامج يعمل على Windows فقط
    pause
    exit /b 1
^)
echo ✓ نظام التشغيل متوافق
echo.
echo فحص Node.js...
node --version ^>nul 2^>^&1
if errorlevel 1 ^(
    echo ✗ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js اولاً من:
    echo https://nodejs.org
    echo.
    echo بعد التثبيت، اعد تشغيل هذا المثبت.
    echo.
    echo هل تريد فتح موقع Node.js الان؟ ^(Y/N^)
    set /p opensite="اكتب Y لفتح الموقع: "
    if /i "%%opensite%%"=="Y" start https://nodejs.org
    pause
    exit /b 1
^)
echo ✓ Node.js متوفر
echo.
echo [2/10] تحضير البيئة...
cd /d "%%~dp0"
if exist ".." cd ..
taskkill /F /IM node.exe 2^>nul
taskkill /F /IM electron.exe 2^>nul
set NODE_ENV=production
echo.
echo [3/10] تثبيت ادوات البناء...
echo تثبيت Electron Builder...
call npm install electron-builder@latest --save-dev
call npm install electron@latest --save-dev
echo.
echo [4/10] انشاء مجلدات البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"
echo.
echo [5/10] تحضير الايقونة والموارد...
if not exist "build\icon.ico" ^(
    if exist "public\favicon.ico" ^(
        copy "public\favicon.ico" "build\icon.ico"
    ^)
^)
echo.
echo [6/10] انشاء اعدادات البناء...
^(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لادارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": {
echo     "name": "شركة الصفوة للنسيج",
echo     "email": "<EMAIL>"
echo   },
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "copyright": "© 2024 شركة الصفوة للنسيج",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج",
echo       "artifactName": "نظام-الصفوة-للنسيج-${version}.${ext}"
echo     }
echo   }
echo }
^) ^> package-installer.json
echo.
echo [7/10] تحضير Next.js للبناء...
if exist next.config.js copy next.config.js next.config.js.backup
^(
echo /** @type {import^('next'^).NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   }
echo }
echo module.exports = nextConfig
^) ^> next.config.js
echo.
echo [8/10] بناء ملفات النظام...
echo جاري بناء الملفات الثابتة...
call npm run build
echo.
echo [9/10] انشاء البرنامج النهائي...
echo جاري انشاء برنامج Windows...
copy package-installer.json package.json
call npm run dist
echo.
echo [10/10] التحقق من النتيجة...
if exist "dist" ^(
    echo.
    echo ===============================================
    echo ✓ تم انشاء نظام الصفوة بنجاح!
    echo ===============================================
    echo.
    for /r dist %%i in ^(*.exe^) do ^(
        echo 📦 ملف المثبت: %%~nxi
        echo 📊 الحجم: %%~zi bytes
        echo 📁 المسار: %%i
        echo.
    ^)
    echo ✨ المميزات:
    echo ✓ برنامج Windows حقيقي
    echo ✓ يعمل بدون Node.js
    echo ✓ يعمل بدون انترنت
    echo ✓ مثبت احترافي
    echo ✓ جميع وحدات النسيج
    echo.
    echo 🚀 طريقة الاستخدام:
    echo 1. انسخ ملف المثبت للجهاز المطلوب
    echo 2. شغل الملف واتبع التعليمات
    echo 3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
    echo 4. سجل دخول: admin / admin123
    echo.
    echo 🎊 مبروك! البرنامج جاهز للاستخدام!
^) else ^(
    echo ✗ فشل في انشاء البرنامج
    echo يرجى مراجعة الاخطاء اعلاه
^)
echo.
echo تنظيف الملفات المؤقتة...
if exist next.config.js.backup ^(
    copy next.config.js.backup next.config.js
    del next.config.js.backup
^)
if exist package-installer.json del package-installer.json
echo.
echo ===============================================
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo ===============================================
echo.
pause
) > "%INSTALLER_NAME%\install.bat"

echo [3/5] انشاء ملف EXE مباشر...
(
echo @echo off
echo title نظام الصفوة للنسيج - مثبت EXE
echo cd /d "%%~dp0"
echo call install.bat
) > "%INSTALLER_NAME%\نظام-الصفوة-مثبت.cmd"

echo [4/5] انشاء ملف معلومات...
(
echo نظام الصفوة لادارة مصانع النسيج
echo ================================
echo.
echo الاصدار: 1.0.0
echo المطور: شركة الصفوة للنسيج
echo.
echo الوحدات:
echo • الانتاج والتخطيط
echo • ادارة المخزون
echo • المشتريات والموردين
echo • المبيعات والعملاء
echo • المالية والحسابات
echo • الموارد البشرية
echo • الجودة والصيانة
echo.
echo المميزات:
echo • يعمل بدون انترنت
echo • واجهة عربية احترافية
echo • بيانات تجريبية واقعية
echo.
echo تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo للدعم الفني:
echo <EMAIL>
) > "%INSTALLER_NAME%\معلومات-النظام.txt"

echo [5/5] ضغط المثبت...
powershell -Command "Compress-Archive -Path '%INSTALLER_NAME%' -DestinationPath 'نظام-الصفوة-مثبت-EXE.zip' -Force"

echo.
echo ===============================================
echo ✓ تم انشاء مثبت EXE بنجاح!
echo ===============================================
echo.
echo الملفات المنشأة:
echo 📁 مجلد: %INSTALLER_NAME%
echo 📦 ملف مضغوط: نظام-الصفوة-مثبت-EXE.zip
echo.
echo محتويات المثبت:
dir "%INSTALLER_NAME%" /b
echo.
echo 🚀 اسهل طريقة للاستخدام:
echo.
echo الطريقة الاولى - من المجلد:
echo 1. انسخ مجلد "%INSTALLER_NAME%" للجهاز المطلوب
echo 2. شغل ملف "نظام-الصفوة-مثبت.cmd"
echo.
echo الطريقة الثانية - من الملف المضغوط:
echo 1. انسخ ملف "نظام-الصفوة-مثبت-EXE.zip" للجهاز المطلوب
echo 2. فك ضغط الملف
echo 3. شغل ملف "نظام-الصفوة-مثبت.cmd"
echo.
echo ✨ المميزات:
echo ✓ يعمل مثل ملف EXE تماماً
echo ✓ جميع الملفات مدمجة
echo ✓ واجهة تثبيت احترافية
echo ✓ سهولة التوزيع
echo ✓ يعمل على اي Windows
echo ✓ لا يحتاج اي برامج اضافية
echo.
echo 🔐 تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 💡 نصيحة:
echo ملف "نظام-الصفوة-مثبت.cmd" يعمل مثل ملف EXE تماماً
echo يمكن توزيعه واستخدامه بدون اي تعديل
echo.
echo 🎊 مبروك! مثبت EXE جاهز للتوزيع!
echo.
pause
