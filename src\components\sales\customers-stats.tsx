import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, UserCheck, Star, TrendingUp } from 'lucide-react'

const stats = [
  {
    title: 'إجمالي العملاء',
    value: '156',
    change: '+12',
    changeType: 'positive' as const,
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    title: 'عملاء نشطين',
    value: '89',
    change: '+8',
    changeType: 'positive' as const,
    icon: UserCheck,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    title: 'عملاء مميزين',
    value: '23',
    change: '+3',
    changeType: 'positive' as const,
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    title: 'نمو العملاء',
    value: '15%',
    change: '+2%',
    changeType: 'positive' as const,
    icon: TrendingUp,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
]

export function CustomersStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-full ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : stat.changeType === 'negative'
                ? 'text-red-600'
                : 'text-muted-foreground'
            }`}>
              {stat.change} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
