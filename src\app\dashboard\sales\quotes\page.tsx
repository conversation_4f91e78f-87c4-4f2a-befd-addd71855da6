'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  FileText,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Send
} from 'lucide-react'

interface QuoteItem {
  id: string
  productCode: string
  productName: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
  description?: string
}

interface Quote {
  id: string
  quoteNumber: string
  customerName: string
  customerEmail: string
  customerPhone: string
  quoteDate: Date
  validUntil: Date
  status: string
  totalAmount: number
  discount: number
  finalAmount: number
  notes?: string
  createdBy: string
  items: QuoteItem[]
}

const mockQuotes: Quote[] = [
  {
    id: '1',
    quoteNumber: 'QT-2024-001',
    customerName: 'شركة النسيج المتطور',
    customerEmail: '<EMAIL>',
    customerPhone: '+966501234567',
    quoteDate: new Date('2024-01-15'),
    validUntil: new Date('2024-02-15'),
    status: 'PENDING',
    totalAmount: 15000,
    discount: 750,
    finalAmount: 14250,
    notes: 'عرض خاص للعميل الجديد',
    createdBy: 'مدير المبيعات',
    items: [
      {
        id: '1',
        productCode: 'PRD001',
        productName: 'قميص قطني أزرق',
        quantity: 100,
        unit: 'قطعة',
        unitPrice: 75,
        totalPrice: 7500,
        description: 'قميص قطني عالي الجودة'
      },
      {
        id: '2',
        productCode: 'PRD002',
        productName: 'قميص قطني أحمر',
        quantity: 100,
        unit: 'قطعة',
        unitPrice: 75,
        totalPrice: 7500,
        description: 'قميص قطني عالي الجودة'
      }
    ]
  },
  {
    id: '2',
    quoteNumber: 'QT-2024-002',
    customerName: 'مؤسسة الأزياء العصرية',
    customerEmail: '<EMAIL>',
    customerPhone: '+966507654321',
    quoteDate: new Date('2024-01-16'),
    validUntil: new Date('2024-02-16'),
    status: 'APPROVED',
    totalAmount: 25000,
    discount: 0,
    finalAmount: 25000,
    createdBy: 'مندوب المبيعات',
    items: [
      {
        id: '3',
        productCode: 'PRD003',
        productName: 'بنطلون جينز',
        quantity: 200,
        unit: 'قطعة',
        unitPrice: 125,
        totalPrice: 25000,
        description: 'بنطلون جينز عالي الجودة'
      }
    ]
  },
  {
    id: '3',
    quoteNumber: 'QT-2024-003',
    customerName: 'متجر الأناقة',
    customerEmail: '<EMAIL>',
    customerPhone: '+966509876543',
    quoteDate: new Date('2024-01-17'),
    validUntil: new Date('2024-01-31'),
    status: 'EXPIRED',
    totalAmount: 8000,
    discount: 400,
    finalAmount: 7600,
    notes: 'انتهت صلاحية العرض',
    createdBy: 'مدير المبيعات',
    items: [
      {
        id: '4',
        productCode: 'PRD001',
        productName: 'قميص قطني أزرق',
        quantity: 80,
        unit: 'قطعة',
        unitPrice: 100,
        totalPrice: 8000,
        description: 'قميص قطني مميز'
      }
    ]
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING': return 'bg-yellow-500'
    case 'APPROVED': return 'bg-green-500'
    case 'REJECTED': return 'bg-red-500'
    case 'EXPIRED': return 'bg-gray-500'
    case 'CONVERTED': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return 'في الانتظار'
    case 'APPROVED': return 'معتمد'
    case 'REJECTED': return 'مرفوض'
    case 'EXPIRED': return 'منتهي الصلاحية'
    case 'CONVERTED': return 'تم التحويل'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PENDING': return <Clock className="h-4 w-4" />
    case 'APPROVED': return <CheckCircle className="h-4 w-4" />
    case 'REJECTED': return <XCircle className="h-4 w-4" />
    case 'EXPIRED': return <XCircle className="h-4 w-4" />
    case 'CONVERTED': return <CheckCircle className="h-4 w-4" />
    default: return <FileText className="h-4 w-4" />
  }
}

export default function QuotesPage() {
  const [quotes, setQuotes] = useState<Quote[]>(mockQuotes)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.customerName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || quote.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const handleApprove = (quoteId: string) => {
    setQuotes(quotes.map(quote => 
      quote.id === quoteId ? { ...quote, status: 'APPROVED' } : quote
    ))
  }

  const handleReject = (quoteId: string) => {
    setQuotes(quotes.map(quote => 
      quote.id === quoteId ? { ...quote, status: 'REJECTED' } : quote
    ))
  }

  const totalPending = quotes.filter(q => q.status === 'PENDING').length
  const totalApproved = quotes.filter(q => q.status === 'APPROVED').length
  const totalValue = quotes.reduce((acc, q) => acc + q.finalAmount, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">عروض الأسعار</h1>
          <p className="text-gray-600">إدارة عروض الأسعار للعملاء</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            عرض سعر جديد
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي العروض</p>
                <p className="text-2xl font-bold text-blue-600">{quotes.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">{totalPending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معتمد</p>
                <p className="text-2xl font-bold text-green-600">{totalApproved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي القيمة</p>
                <p className="text-2xl font-bold text-purple-600">
                  {totalValue.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في عروض الأسعار..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="PENDING">في الانتظار</option>
                <option value="APPROVED">معتمد</option>
                <option value="REJECTED">مرفوض</option>
                <option value="EXPIRED">منتهي الصلاحية</option>
                <option value="CONVERTED">تم التحويل</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quotes List */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة عروض الأسعار</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredQuotes.map((quote) => (
              <div key={quote.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(quote.status)}>
                      {getStatusIcon(quote.status)}
                      <span className="mr-1">{getStatusText(quote.status)}</span>
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{quote.quoteNumber}</h3>
                      <p className="text-sm text-gray-600">{quote.customerName}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {quote.status === 'PENDING' && (
                      <>
                        <Button 
                          size="sm"
                          onClick={() => handleApprove(quote.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 ml-1" />
                          اعتماد
                        </Button>
                        <Button 
                          size="sm"
                          variant="outline"
                          onClick={() => handleReject(quote.id)}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="h-4 w-4 ml-1" />
                          رفض
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="outline">
                      <Send className="h-4 w-4 ml-1" />
                      إرسال
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">تاريخ العرض</p>
                    <p className="font-medium">{quote.quoteDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">صالح حتى</p>
                    <p className="font-medium">{quote.validUntil.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المبلغ الإجمالي</p>
                    <p className="font-medium">{quote.totalAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المبلغ النهائي</p>
                    <p className="font-medium text-green-600">{quote.finalAmount.toLocaleString()} ر.س</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">البريد الإلكتروني</p>
                    <p className="font-medium">{quote.customerEmail}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">رقم الهاتف</p>
                    <p className="font-medium">{quote.customerPhone}</p>
                  </div>
                </div>

                {/* Items Summary */}
                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-2">الأصناف ({quote.items.length}):</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {quote.items.slice(0, 2).map((item) => (
                      <div key={item.id} className="text-sm bg-gray-100 rounded p-2">
                        <span className="font-medium">{item.productName}</span>
                        <span className="text-gray-600"> - {item.quantity} {item.unit}</span>
                        <span className="text-green-600"> ({item.totalPrice.toLocaleString()} ر.س)</span>
                      </div>
                    ))}
                    {quote.items.length > 2 && (
                      <div className="text-sm text-gray-500 p-2">
                        و {quote.items.length - 2} أصناف أخرى...
                      </div>
                    )}
                  </div>
                </div>

                {quote.discount > 0 && (
                  <div className="mb-3 p-2 bg-green-100 rounded text-sm">
                    <strong>خصم:</strong> {quote.discount.toLocaleString()} ر.س
                  </div>
                )}

                {quote.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {quote.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredQuotes.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد عروض أسعار تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
