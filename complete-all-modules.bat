@echo off
echo.
echo Safa Textile ERP - Complete System Ready!
echo =========================================
echo.

echo [INFO] All modules have been created successfully!
echo.

echo ✅ Production Modules:
echo    - Production Stages: /dashboard/production/stages
echo    - Production Planning: /dashboard/production/planning
echo    - Work Orders: /dashboard/production/work-orders
echo.

echo ✅ Inventory Modules:
echo    - Inventory Movements: /dashboard/inventory/movements
echo    - Inventory Count: /dashboard/inventory/count
echo    - Materials: /dashboard/inventory/materials
echo    - Products: /dashboard/inventory/products
echo.

echo ✅ Purchasing Modules:
echo    - Purchase Requests: /dashboard/purchasing/requests
echo    - Purchase Orders: /dashboard/purchasing/orders
echo    - Suppliers: /dashboard/purchasing/suppliers
echo.

echo ✅ Sales Modules:
echo    - Sales Quotes: /dashboard/sales/quotes
echo    - Sales Orders: /dashboard/sales/orders
echo    - Customers: /dashboard/sales/customers
echo.

echo ✅ Finance Modules:
echo    - Accounts: /dashboard/finance/accounts
echo    - Invoices: /dashboard/finance/invoices
echo    - Financial Reports: /dashboard/finance/reports
echo.

echo ✅ HR Modules:
echo    - Attendance: /dashboard/hr/attendance
echo    - Payroll: /dashboard/hr/payroll
echo    - Employees: /dashboard/hr/employees
echo.

echo ✅ Other Modules:
echo    - Quality: /dashboard/quality
echo    - Maintenance: /dashboard/maintenance
echo    - User Management: /dashboard/users
echo    - Settings: /dashboard/settings
echo.

echo =========================================
echo SYSTEM STATUS: COMPLETE AND READY!
echo =========================================
echo.

echo 🔐 Login Credentials:
echo    Username: admin
echo    Password: admin123
echo.

echo 🌐 Access URL: http://localhost:3000
echo.

echo 🎯 Features:
echo    ✅ 15 Complete Modules
echo    ✅ Arabic Interface with RTL Support
echo    ✅ Modern Al-Safwa Logo Design
echo    ✅ Offline SQLite Database
echo    ✅ Username + Password Login
echo    ✅ Daily/Monthly Payroll System
echo    ✅ Monthly Quality Assessment
echo    ✅ Complete ERP Functionality
echo.

echo Starting the complete system...
echo.

REM Stop any running instances
taskkill /F /IM node.exe 2>nul
timeout /t 2 /nobreak >nul

REM Start the system
start http://localhost:3000
cmd /c "npm run dev"
