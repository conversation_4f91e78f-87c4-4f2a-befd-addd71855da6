import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Clock, CheckCircle, Truck, DollarSign } from 'lucide-react'

const stats = [
  {
    title: 'أوامر معلقة',
    value: '12',
    change: '+3',
    changeType: 'neutral' as const,
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    title: 'أوامر معتمدة',
    value: '8',
    change: '+2',
    changeType: 'positive' as const,
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    title: 'قيد التوريد',
    value: '5',
    change: '+1',
    changeType: 'positive' as const,
    icon: Truck,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    title: 'إجمالي القيمة',
    value: '245,000',
    change: '+15%',
    changeType: 'positive' as const,
    icon: DollarSign,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    suffix: 'ريال',
  },
]

export function PurchaseOrdersStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-full ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stat.value}
              {stat.suffix && <span className="text-sm font-normal text-muted-foreground ml-1">{stat.suffix}</span>}
            </div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : stat.changeType === 'negative'
                ? 'text-red-600'
                : 'text-muted-foreground'
            }`}>
              {stat.change} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
