import type { Metadata } from 'next'
import { Cairo, Inter } from 'next/font/google'
import './globals.css'
import './modern-theme.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  variable: '--font-cairo',
  display: 'swap',
})

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'شركة الصفوة للنسيج - نظام إدارة موارد المؤسسة',
  description: 'نظام إدارة موارد المؤسسة المتكامل للنسيج والملابس - شركة الصفوة',
  keywords: ['الصفوة', 'نسيج', 'إدارة', 'موارد', 'مؤسسة', 'ERP', 'textile', 'مصنع', 'إنتاج', 'مخزون'],
  authors: [{ name: 'شركة الصفوة للنسيج' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'noindex, nofollow', // للتطوير فقط
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={`${cairo.variable} ${inter.variable}`}>
      <body className="font-arabic antialiased bg-gray-50 font-modern">
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
