import type { Metadata } from 'next'
import { Cairo } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  variable: '--font-cairo',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'نظام إدارة موارد المؤسسة للنسيج',
  description: 'نظام ERP شامل لإدارة مصانع النسيج والملابس',
  keywords: ['ERP', 'نسيج', 'إدارة', 'مصنع', 'إنتاج', 'مخزون'],
  authors: [{ name: 'Textile ERP Team' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'noindex, nofollow', // للتطوير فقط
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl" className={cairo.variable}>
      <body className="font-arabic antialiased">
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
