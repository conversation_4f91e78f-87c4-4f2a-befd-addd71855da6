'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  FileText,
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Send,
  Printer
} from 'lucide-react'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number
}

interface Invoice {
  id: string
  invoiceNumber: string
  type: string
  customerName?: string
  supplierName?: string
  invoiceDate: Date
  dueDate: Date
  status: string
  subtotal: number
  taxAmount: number
  discountAmount: number
  totalAmount: number
  paidAmount: number
  remainingAmount: number
  notes?: string
  createdBy: string
  items: InvoiceItem[]
}

const mockInvoices: Invoice[] = [
  {
    id: '1',
    invoiceNumber: 'INV-S-2024-001',
    type: 'SALES',
    customerName: 'شركة النسيج المتطور',
    invoiceDate: new Date('2024-01-15'),
    dueDate: new Date('2024-02-15'),
    status: 'PAID',
    subtotal: 15000,
    taxAmount: 2250,
    discountAmount: 750,
    totalAmount: 16500,
    paidAmount: 16500,
    remainingAmount: 0,
    notes: 'فاتورة بيع للعميل الرئيسي',
    createdBy: 'مدير المبيعات',
    items: [
      {
        id: '1',
        description: 'قميص قطني أزرق',
        quantity: 100,
        unit: 'قطعة',
        unitPrice: 75,
        totalPrice: 7500
      },
      {
        id: '2',
        description: 'قميص قطني أحمر',
        quantity: 100,
        unit: 'قطعة',
        unitPrice: 75,
        totalPrice: 7500
      }
    ]
  },
  {
    id: '2',
    invoiceNumber: 'INV-P-2024-001',
    type: 'PURCHASE',
    supplierName: 'مؤسسة المواد الخام',
    invoiceDate: new Date('2024-01-16'),
    dueDate: new Date('2024-02-16'),
    status: 'PENDING',
    subtotal: 25000,
    taxAmount: 3750,
    discountAmount: 0,
    totalAmount: 28750,
    paidAmount: 0,
    remainingAmount: 28750,
    notes: 'فاتورة شراء مواد خام',
    createdBy: 'مدير المشتريات',
    items: [
      {
        id: '3',
        description: 'قطن خام عالي الجودة',
        quantity: 1000,
        unit: 'كيلو',
        unitPrice: 15.50,
        totalPrice: 15500
      },
      {
        id: '4',
        description: 'صبغة زرقاء',
        quantity: 200,
        unit: 'لتر',
        unitPrice: 45,
        totalPrice: 9000
      }
    ]
  },
  {
    id: '3',
    invoiceNumber: 'INV-S-2024-002',
    type: 'SALES',
    customerName: 'متجر الأناقة',
    invoiceDate: new Date('2024-01-17'),
    dueDate: new Date('2024-02-01'),
    status: 'OVERDUE',
    subtotal: 8000,
    taxAmount: 1200,
    discountAmount: 400,
    totalAmount: 8800,
    paidAmount: 4000,
    remainingAmount: 4800,
    notes: 'فاتورة متأخرة السداد',
    createdBy: 'مندوب المبيعات',
    items: [
      {
        id: '5',
        description: 'بنطلون جينز',
        quantity: 80,
        unit: 'قطعة',
        unitPrice: 100,
        totalPrice: 8000
      }
    ]
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'bg-gray-500'
    case 'SENT': return 'bg-blue-500'
    case 'PENDING': return 'bg-yellow-500'
    case 'PAID': return 'bg-green-500'
    case 'OVERDUE': return 'bg-red-500'
    case 'CANCELLED': return 'bg-gray-600'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'مسودة'
    case 'SENT': return 'مرسلة'
    case 'PENDING': return 'في الانتظار'
    case 'PAID': return 'مدفوعة'
    case 'OVERDUE': return 'متأخرة'
    case 'CANCELLED': return 'ملغية'
    default: return 'غير محدد'
  }
}

const getTypeColor = (type: string) => {
  switch (type) {
    case 'SALES': return 'bg-green-500'
    case 'PURCHASE': return 'bg-blue-500'
    default: return 'bg-gray-500'
  }
}

const getTypeText = (type: string) => {
  switch (type) {
    case 'SALES': return 'فاتورة بيع'
    case 'PURCHASE': return 'فاتورة شراء'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PAID': return <CheckCircle className="h-4 w-4" />
    case 'PENDING': return <Clock className="h-4 w-4" />
    case 'OVERDUE': return <AlertTriangle className="h-4 w-4" />
    default: return <FileText className="h-4 w-4" />
  }
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>(mockInvoices)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (invoice.customerName && invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (invoice.supplierName && invoice.supplierName.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = selectedStatus === 'all' || invoice.status === selectedStatus
    const matchesType = selectedType === 'all' || invoice.type === selectedType
    return matchesSearch && matchesStatus && matchesType
  })

  const handleMarkAsPaid = (invoiceId: string) => {
    setInvoices(invoices.map(invoice => 
      invoice.id === invoiceId 
        ? { 
            ...invoice, 
            status: 'PAID',
            paidAmount: invoice.totalAmount,
            remainingAmount: 0
          }
        : invoice
    ))
  }

  const totalInvoices = invoices.length
  const totalSales = invoices.filter(i => i.type === 'SALES').reduce((acc, i) => acc + i.totalAmount, 0)
  const totalPurchases = invoices.filter(i => i.type === 'PURCHASE').reduce((acc, i) => acc + i.totalAmount, 0)
  const totalOverdue = invoices.filter(i => i.status === 'OVERDUE').reduce((acc, i) => acc + i.remainingAmount, 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الفواتير</h1>
          <p className="text-gray-600">إدارة فواتير البيع والشراء</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            فاتورة جديدة
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                <p className="text-2xl font-bold text-blue-600">{totalInvoices}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalSales.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                <p className="text-2xl font-bold text-blue-600">
                  {totalPurchases.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المتأخرات</p>
                <p className="text-2xl font-bold text-red-600">
                  {totalOverdue.toLocaleString()} ر.س
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الفواتير..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="SALES">فواتير البيع</option>
                <option value="PURCHASE">فواتير الشراء</option>
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="DRAFT">مسودة</option>
                <option value="SENT">مرسلة</option>
                <option value="PENDING">في الانتظار</option>
                <option value="PAID">مدفوعة</option>
                <option value="OVERDUE">متأخرة</option>
                <option value="CANCELLED">ملغية</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices List */}
      <Card>
        <CardHeader>
          <CardTitle>قائمة الفواتير</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredInvoices.map((invoice) => (
              <div key={invoice.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getTypeColor(invoice.type)}>
                      {getTypeText(invoice.type)}
                    </Badge>
                    <Badge className={getStatusColor(invoice.status)}>
                      {getStatusIcon(invoice.status)}
                      <span className="mr-1">{getStatusText(invoice.status)}</span>
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{invoice.invoiceNumber}</h3>
                      <p className="text-sm text-gray-600">
                        {invoice.customerName || invoice.supplierName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {invoice.status === 'PENDING' && (
                      <Button 
                        size="sm"
                        onClick={() => handleMarkAsPaid(invoice.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 ml-1" />
                        تسديد
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Printer className="h-4 w-4 ml-1" />
                      طباعة
                    </Button>
                    <Button size="sm" variant="outline">
                      <Send className="h-4 w-4 ml-1" />
                      إرسال
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">تاريخ الفاتورة</p>
                    <p className="font-medium">{invoice.invoiceDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">تاريخ الاستحقاق</p>
                    <p className="font-medium">{invoice.dueDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المبلغ الإجمالي</p>
                    <p className="font-medium">{invoice.totalAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المبلغ المتبقي</p>
                    <p className={`font-medium ${invoice.remainingAmount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {invoice.remainingAmount.toLocaleString()} ر.س
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">المبلغ الفرعي</p>
                    <p className="font-medium">{invoice.subtotal.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الضريبة</p>
                    <p className="font-medium">{invoice.taxAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الخصم</p>
                    <p className="font-medium">{invoice.discountAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المبلغ المدفوع</p>
                    <p className="font-medium text-green-600">{invoice.paidAmount.toLocaleString()} ر.س</p>
                  </div>
                </div>

                {/* Items Summary */}
                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-2">الأصناف ({invoice.items.length}):</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {invoice.items.slice(0, 2).map((item) => (
                      <div key={item.id} className="text-sm bg-gray-100 rounded p-2">
                        <span className="font-medium">{item.description}</span>
                        <span className="text-gray-600"> - {item.quantity} {item.unit}</span>
                        <span className="text-green-600"> ({item.totalPrice.toLocaleString()} ر.س)</span>
                      </div>
                    ))}
                    {invoice.items.length > 2 && (
                      <div className="text-sm text-gray-500 p-2">
                        و {invoice.items.length - 2} أصناف أخرى...
                      </div>
                    )}
                  </div>
                </div>

                {invoice.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {invoice.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredInvoices.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد فواتير تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
