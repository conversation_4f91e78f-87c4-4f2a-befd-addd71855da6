@echo off
echo.
echo Safa Textile ERP - Offline Setup for Windows
echo =============================================
echo.

echo [1/5] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not installed
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)
echo OK: Node.js is available

echo.
echo [2/5] Setting up offline configuration...
copy "next.config.offline.js" "next.config.js" >nul 2>&1

echo DATABASE_URL="file:./data/database.db" > .env
echo NEXTAUTH_URL="http://localhost:3000" >> .env
echo NEXTAUTH_SECRET="safa-textile-offline-2024" >> .env
echo OFFLINE_MODE="true" >> .env

echo OK: Offline configuration created

echo.
echo [3/5] Creating local database...
if not exist "data" mkdir data
cmd /c "npx prisma generate"
cmd /c "npx prisma db push"

if %errorlevel% equ 0 (
    echo OK: Local database created
) else (
    echo WARNING: Database setup had issues, but continuing...
)

echo.
echo [4/5] Adding sample data...
cmd /c "npx prisma db seed"

echo.
echo [5/5] Building system for offline use...
cmd /c "npm run build"

echo.
echo =============================================
echo SUCCESS: Offline setup completed!
echo =============================================
echo.
echo System is ready to run offline on Windows
echo Database: SQLite (local file)
echo URL: http://localhost:3000
echo.
echo Login credentials:
echo - Admin: <EMAIL> / admin123
echo - Supervisor: <EMAIL> / super123
echo - Operator: <EMAIL> / oper123
echo.
echo To start the system: run start-offline.bat
echo.
pause
