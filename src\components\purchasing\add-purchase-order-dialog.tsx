'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Calendar, Plus, Trash2 } from 'lucide-react'

const purchaseOrderSchema = z.object({
  orderNumber: z.string().min(1, 'رقم الأمر مطلوب'),
  supplierId: z.string().min(1, 'المورد مطلوب'),
  orderDate: z.string().min(1, 'تاريخ الطلب مطلوب'),
  expectedDate: z.string().min(1, 'التاريخ المتوقع مطلوب'),
  notes: z.string().optional(),
  items: z.array(z.object({
    materialId: z.string().min(1, 'المادة مطلوبة'),
    quantity: z.number().min(1, 'الكمية يجب أن تكون أكبر من 0'),
    unitPrice: z.number().min(0, 'سعر الوحدة يجب أن يكون أكبر من أو يساوي 0'),
  })).min(1, 'يجب إضافة صنف واحد على الأقل'),
})

type PurchaseOrderFormData = z.infer<typeof purchaseOrderSchema>

interface AddPurchaseOrderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// بيانات تجريبية للموردين
const mockSuppliers = [
  { id: '1', code: 'SUP001', name: 'شركة القطن المصري' },
  { id: '2', code: 'SUP002', name: 'مصنع الألوان الحديث' },
  { id: '3', code: 'SUP003', name: 'شركة الخيوط المتقدمة' },
]

// بيانات تجريبية للمواد
const mockMaterials = [
  { id: '1', code: 'MAT001', name: 'قطن خام عالي الجودة', unit: 'كيلو' },
  { id: '2', code: 'MAT002', name: 'صبغة زرقاء', unit: 'لتر' },
  { id: '3', code: 'MAT003', name: 'صبغة حمراء', unit: 'لتر' },
  { id: '4', code: 'MAT004', name: 'خيوط بوليستر', unit: 'كيلو' },
]

export function AddPurchaseOrderDialog({ open, onOpenChange }: AddPurchaseOrderDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<PurchaseOrderFormData>({
    resolver: zodResolver(purchaseOrderSchema),
    defaultValues: {
      orderNumber: '',
      supplierId: '',
      orderDate: '',
      expectedDate: '',
      notes: '',
      items: [{ materialId: '', quantity: 1, unitPrice: 0 }],
    },
  })

  const onSubmit = async (data: PurchaseOrderFormData) => {
    setIsLoading(true)
    
    try {
      // حساب إجمالي المبلغ
      const totalAmount = data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
      
      // هنا سيتم إرسال البيانات إلى API
      console.log('Purchase Order data:', { ...data, totalAmount })
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'تم إنشاء أمر الشراء بنجاح',
        description: `تم إنشاء أمر الشراء ${data.orderNumber}`,
      })
      
      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'خطأ في إنشاء أمر الشراء',
        description: 'حدث خطأ أثناء إنشاء أمر الشراء. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // توليد رقم أمر شراء تلقائي
  const generateOrderNumber = () => {
    const timestamp = Date.now().toString().slice(-6)
    const orderNumber = `PO-2024-${timestamp}`
    form.setValue('orderNumber', orderNumber)
  }

  // إضافة صنف جديد
  const addItem = () => {
    const currentItems = form.getValues('items')
    form.setValue('items', [...currentItems, { materialId: '', quantity: 1, unitPrice: 0 }])
  }

  // حذف صنف
  const removeItem = (index: number) => {
    const currentItems = form.getValues('items')
    if (currentItems.length > 1) {
      form.setValue('items', currentItems.filter((_, i) => i !== index))
    }
  }

  // حساب إجمالي المبلغ
  const items = form.watch('items')
  const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>إنشاء أمر شراء جديد</DialogTitle>
          <DialogDescription>
            أدخل تفاصيل أمر الشراء الجديد والأصناف المطلوبة
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* المعلومات الأساسية */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="orderNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رقم أمر الشراء *</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Input placeholder="PO-2024-001" {...field} />
                      </FormControl>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={generateOrderNumber}
                        size="sm"
                      >
                        توليد
                      </Button>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>المورد *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر المورد" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockSuppliers.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id}>
                            {supplier.code} - {supplier.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="orderDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>تاريخ الطلب *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="date"
                          {...field}
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expectedDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>التاريخ المتوقع للتسليم *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input 
                          type="date"
                          {...field}
                        />
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* الأصناف */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">الأصناف المطلوبة</h3>
                <Button type="button" variant="outline" onClick={addItem} size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة صنف
                </Button>
              </div>

              <div className="space-y-3">
                {items.map((_, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-end p-4 border rounded-lg">
                    <div className="col-span-5">
                      <FormField
                        control={form.control}
                        name={`items.${index}.materialId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>المادة *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="اختر المادة" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {mockMaterials.map((material) => (
                                  <SelectItem key={material.id} value={material.id}>
                                    {material.code} - {material.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="col-span-2">
                      <FormField
                        control={form.control}
                        name={`items.${index}.quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>الكمية *</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                placeholder="100"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="col-span-2">
                      <FormField
                        control={form.control}
                        name={`items.${index}.unitPrice`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>سعر الوحدة *</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                step="0.01"
                                placeholder="15.50"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="col-span-2">
                      <FormLabel>الإجمالي</FormLabel>
                      <div className="h-10 px-3 py-2 border rounded-md bg-muted flex items-center">
                        <span className="text-sm font-medium">
                          {(items[index].quantity * items[index].unitPrice).toFixed(2)} ريال
                        </span>
                      </div>
                    </div>

                    <div className="col-span-1">
                      {items.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* إجمالي المبلغ */}
              <div className="flex justify-end">
                <div className="bg-muted p-4 rounded-lg">
                  <div className="text-lg font-bold">
                    إجمالي المبلغ: {totalAmount.toFixed(2)} ريال
                  </div>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ملاحظات</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="ملاحظات إضافية حول أمر الشراء..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري الإنشاء...
                  </>
                ) : (
                  'إنشاء أمر الشراء'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
