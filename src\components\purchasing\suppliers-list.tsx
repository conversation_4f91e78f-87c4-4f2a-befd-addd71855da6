'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatDate } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, Phone, Mail, MapPin, Building2 } from 'lucide-react'

// بيانات تجريبية للموردين
const mockSuppliers = [
  {
    id: '1',
    code: 'SUP001',
    nameAr: 'شركة القطن المصري',
    nameEn: 'Egyptian Cotton Company',
    contactPerson: 'أحمد محمد',
    phone: '+966501234567',
    email: '<EMAIL>',
    address: 'الرياض، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 45,
    totalAmount: 125000,
    lastOrderDate: new Date('2024-01-20'),
    rating: 4.5,
    category: 'مواد خام',
  },
  {
    id: '2',
    code: 'SUP002',
    nameAr: 'مصنع الألوان الحديث',
    nameEn: 'Modern Dyes Factory',
    contactPerson: 'سارة أحمد',
    phone: '+966502345678',
    email: '<EMAIL>',
    address: 'جدة، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 32,
    totalAmount: 89000,
    lastOrderDate: new Date('2024-01-18'),
    rating: 4.2,
    category: 'أصباغ وكيماويات',
  },
  {
    id: '3',
    code: 'SUP003',
    nameAr: 'شركة الخيوط المتقدمة',
    nameEn: 'Advanced Threads Company',
    contactPerson: 'محمد علي',
    phone: '+966503456789',
    email: '<EMAIL>',
    address: 'الدمام، المملكة العربية السعودية',
    isActive: true,
    totalOrders: 28,
    totalAmount: 67000,
    lastOrderDate: new Date('2024-01-15'),
    rating: 4.0,
    category: 'خيوط ونسيج',
  },
  {
    id: '4',
    code: 'SUP004',
    nameAr: 'مؤسسة قطع الغيار الصناعية',
    nameEn: 'Industrial Spare Parts Est.',
    contactPerson: 'خالد السعد',
    phone: '+966504567890',
    email: '<EMAIL>',
    address: 'الخبر، المملكة العربية السعودية',
    isActive: false,
    totalOrders: 12,
    totalAmount: 23000,
    lastOrderDate: new Date('2023-12-10'),
    rating: 3.5,
    category: 'قطع غيار',
  },
]

export function SuppliersList() {
  const [suppliers] = useState(mockSuppliers)

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800'
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'مواد خام':
        return 'bg-blue-100 text-blue-800'
      case 'أصباغ وكيماويات':
        return 'bg-purple-100 text-purple-800'
      case 'خيوط ونسيج':
        return 'bg-green-100 text-green-800'
      case 'قطع غيار':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRatingStars = (rating: number) => {
    const stars = []
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <span key={i} className={i <= rating ? 'text-yellow-400' : 'text-gray-300'}>
          ★
        </span>
      )
    }
    return stars
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الكود</TableHead>
            <TableHead>اسم المورد</TableHead>
            <TableHead>الفئة</TableHead>
            <TableHead>الشخص المسؤول</TableHead>
            <TableHead>معلومات الاتصال</TableHead>
            <TableHead>إجمالي الطلبات</TableHead>
            <TableHead>آخر طلب</TableHead>
            <TableHead>التقييم</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {suppliers.map((supplier) => (
            <TableRow key={supplier.id}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  {supplier.code}
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{supplier.nameAr}</div>
                  {supplier.nameEn && (
                    <div className="text-sm text-muted-foreground">
                      {supplier.nameEn}
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getCategoryColor(supplier.category)}>
                  {supplier.category}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="text-sm font-medium">{supplier.contactPerson}</div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-sm">
                    <Phone className="h-3 w-3" />
                    {supplier.phone}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    <Mail className="h-3 w-3" />
                    {supplier.email}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    {supplier.address}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div>
                  <div className="font-medium">{supplier.totalOrders} طلب</div>
                  <div className="text-sm text-muted-foreground">
                    {supplier.totalAmount.toLocaleString()} ريال
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {formatDate(supplier.lastOrderDate)}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  {getRatingStars(supplier.rating)}
                  <span className="text-sm text-muted-foreground ml-1">
                    ({supplier.rating})
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <Badge className={getStatusColor(supplier.isActive)}>
                  {supplier.isActive ? 'نشط' : 'غير نشط'}
                </Badge>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Eye className="ml-2 h-4 w-4" />
                      عرض التفاصيل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="ml-2 h-4 w-4" />
                      تعديل
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Phone className="ml-2 h-4 w-4" />
                      اتصال
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Mail className="ml-2 h-4 w-4" />
                      إرسال بريد
                    </DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="ml-2 h-4 w-4" />
                      حذف
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
