# 🚀 دليل التثبيت السريع - نظام الصفوة للنسيج

## 📋 **المتطلبات الأساسية**

### **1. Node.js (مطلوب)**
- **التحميل**: [nodejs.org](https://nodejs.org/)
- **اختر**: النسخة LTS (الموصى بها)
- **الإصدار**: 18.0 أو أحدث

### **2. PostgreSQL (مطلوب)**
- **التحميل**: [postgresql.org](https://www.postgresql.org/download/windows/)
- **الإصدار**: 14.0 أو أحدث

---

## 🔧 **خطوات التثبيت**

### **الخطوة 1: تثبيت Node.js**
1. اذهب إلى [nodejs.org](https://nodejs.org/)
2. حمل النسخة LTS
3. شغل الملف المحمل
4. اتبع خطوات التثبيت
5. أعد تشغيل Command Prompt

### **الخطوة 2: تثبيت PostgreSQL**
1. اذهب إلى [postgresql.org](https://www.postgresql.org/download/windows/)
2. حمل PostgreSQL للـ Windows
3. شغل الملف المحمل
4. اختر كلمة مرور للمستخدم `postgres`
5. احفظ كلمة المرور (ستحتاجها لاحقاً)

### **الخطوة 3: تشغيل النظام**
بعد تثبيت Node.js و PostgreSQL:

```cmd
# 1. افتح Command Prompt في مجلد المشروع
# 2. ثبت المكتبات
npm install

# 3. أنشئ ملف البيئة
copy .env.example .env

# 4. عدل ملف .env (اختياري)
notepad .env

# 5. شغل النظام
npm run dev
```

---

## 🌐 **الوصول للنظام**

بعد التشغيل الناجح:
- **الرابط**: http://localhost:3000

### **🔐 الحسابات التجريبية:**
```
مدير النظام:
📧 <EMAIL>
🔑 admin123

مشرف الإنتاج:
📧 <EMAIL>  
🔑 super123

مشغل الماكينة:
📧 <EMAIL>
🔑 oper123
```

---

## 🎨 **ما ستراه:**

### **✨ التصميم الجديد:**
- **شعار شركة الصفوة** المتطور
- **ألوان عصرية** (أزرق متدرج)
- **تأثيرات بصرية** متقدمة
- **واجهة تفاعلية** وسلسة

### **📊 الوحدات المتاحة:**
- **لوحة التحكم** - إحصائيات شاملة
- **الإنتاج** - إدارة أوامر العمل
- **المخزون** - إدارة المواد والمنتجات
- **المشتريات** - إدارة الموردين والطلبات
- **المبيعات** - إدارة العملاء والمبيعات
- **الجودة** - مراقبة ومعايير الجودة
- **المالية** - إدارة الحسابات والفواتير
- **الموارد البشرية** - إدارة الموظفين

---

## 🐛 **حل المشاكل الشائعة**

### **"npm not found"**
- تأكد من تثبيت Node.js
- أعد تشغيل Command Prompt
- تحقق من إضافة Node.js للـ PATH

### **"database connection failed"**
- تأكد من تشغيل PostgreSQL
- تحقق من كلمة المرور في ملف .env
- تأكد من وجود قاعدة البيانات

### **"Port 3000 already in use"**
```cmd
# شغل على منفذ آخر
npm run dev -- -p 3001
```

---

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. راجع قسم حل المشاكل أعلاه
3. تحقق من ملفات السجل

---

## 🎯 **النتيجة المتوقعة**

بعد إكمال الخطوات:
- ✅ النظام يعمل على http://localhost:3000
- ✅ شعار الصفوة يظهر بتصميم عصري
- ✅ جميع الوحدات تعمل بشكل مثالي
- ✅ واجهة عربية متطورة مع دعم RTL

**🎉 استمتع بنظام الصفوة الجديد!**
