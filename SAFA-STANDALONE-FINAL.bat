@echo off
title نظام الصفوة للنسيج - التطبيق المستقل النهائي
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           نظام الصفوة للنسيج - التطبيق المستقل النهائي
echo                              🖥️ بدون خادم - بدون إنترنت 🖥️
echo.
echo ===============================================================================
echo.

echo [1/4] إيقاف العمليات السابقة...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM electron.exe 2>nul
timeout /t 2 /nobreak >nul

echo [2/4] تحضير البيئة...
set NODE_ENV=production
set STANDALONE_MODE=true
set ELECTRON_MODE=true

echo [3/4] تحضير البيانات المحلية...
if not exist "data" mkdir "data"

echo [4/4] تشغيل نظام الصفوة المستقل...
echo.
echo ===============================================
echo 🏭 نظام الصفوة للنسيج - التطبيق المستقل 🏭
echo ===============================================
echo.
echo ✨ المميزات الفريدة:
echo ✓ يعمل بدون خادم ويب
echo ✓ يعمل بدون إنترنت تماماً
echo ✓ بيانات مدمجة ومحلية
echo ✓ أداء فائق وسرعة عالية
echo ✓ واجهة سطح مكتب احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ واجهة عربية مع دعم RTL
echo.
echo 🏭 الوحدات المتاحة:
echo • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo • المالية: الحسابات، الفواتير، التقارير المالية
echo • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo 🔐 تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo 📊 البيانات التجريبية:
echo ✓ مراحل إنتاج واقعية
echo ✓ حركات مخزون مفصلة
echo ✓ طلبات شراء ومبيعات
echo ✓ حسابات مالية شاملة
echo ✓ سجلات موارد بشرية
echo.
echo ===============================================================================
echo.
echo 🚀 جاري تشغيل التطبيق المستقل...
echo.
echo ملاحظة: سيتم تشغيل خادم محلي مؤقت ثم فتح التطبيق في نافذة سطح مكتب
echo.

npx electron .
