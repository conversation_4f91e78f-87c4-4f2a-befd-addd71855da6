'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Calendar } from '@/components/ui/calendar'
import { 
  Calendar as CalendarIcon,
  Clock,
  Users,
  Package,
  TrendingUp,
  Plus,
  Filter,
  Download
} from 'lucide-react'

interface ProductionPlan {
  id: string
  productName: string
  quantity: number
  startDate: Date
  endDate: Date
  priority: string
  status: string
  assignedTeam: string
  estimatedHours: number
  completedHours: number
  materials: string[]
  machines: string[]
}

const mockPlans: ProductionPlan[] = [
  {
    id: '1',
    productName: 'قميص قطني أزرق',
    quantity: 100,
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-01-20'),
    priority: 'HIGH',
    status: 'IN_PROGRESS',
    assignedTeam: 'فريق الإنتاج أ',
    estimatedHours: 40,
    completedHours: 25,
    materials: ['قطن خام', 'صبغة زرقاء'],
    machines: ['نول رقم 1', 'ماكينة صباغة رقم 2']
  },
  {
    id: '2',
    productName: 'قميص قطني أحمر',
    quantity: 150,
    startDate: new Date('2024-01-18'),
    endDate: new Date('2024-01-25'),
    priority: 'MEDIUM',
    status: 'PLANNED',
    assignedTeam: 'فريق الإنتاج ب',
    estimatedHours: 60,
    completedHours: 0,
    materials: ['قطن خام', 'صبغة حمراء'],
    machines: ['نول رقم 2', 'ماكينة صباغة رقم 1']
  },
  {
    id: '3',
    productName: 'بنطلون جينز',
    quantity: 80,
    startDate: new Date('2024-01-22'),
    endDate: new Date('2024-01-30'),
    priority: 'LOW',
    status: 'PLANNED',
    assignedTeam: 'فريق الإنتاج ج',
    estimatedHours: 50,
    completedHours: 0,
    materials: ['قطن دنيم', 'صبغة نيلية'],
    machines: ['نول رقم 3', 'ماكينة قص رقم 1']
  }
]

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'bg-red-500'
    case 'MEDIUM': return 'bg-yellow-500'
    case 'LOW': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'HIGH': return 'عالية'
    case 'MEDIUM': return 'متوسطة'
    case 'LOW': return 'منخفضة'
    default: return 'غير محدد'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PLANNED': return 'bg-blue-500'
    case 'IN_PROGRESS': return 'bg-orange-500'
    case 'COMPLETED': return 'bg-green-500'
    case 'DELAYED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PLANNED': return 'مخطط'
    case 'IN_PROGRESS': return 'قيد التنفيذ'
    case 'COMPLETED': return 'مكتمل'
    case 'DELAYED': return 'متأخر'
    default: return 'غير محدد'
  }
}

export default function ProductionPlanningPage() {
  const [plans, setPlans] = useState<ProductionPlan[]>(mockPlans)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list')

  const totalPlannedHours = plans.reduce((acc, plan) => acc + plan.estimatedHours, 0)
  const totalCompletedHours = plans.reduce((acc, plan) => acc + plan.completedHours, 0)
  const completionRate = totalPlannedHours > 0 ? (totalCompletedHours / totalPlannedHours) * 100 : 0

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">تخطيط الإنتاج</h1>
          <p className="text-gray-600">تخطيط وجدولة عمليات الإنتاج</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Filter className="ml-2 h-4 w-4" />
            تصفية
          </Button>
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            خطة جديدة
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الخطط</p>
                <p className="text-2xl font-bold text-blue-600">{plans.length}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيد التنفيذ</p>
                <p className="text-2xl font-bold text-orange-600">
                  {plans.filter(p => p.status === 'IN_PROGRESS').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معدل الإنجاز</p>
                <p className="text-2xl font-bold text-green-600">
                  {completionRate.toFixed(1)}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الساعات المخططة</p>
                <p className="text-2xl font-bold text-purple-600">{totalPlannedHours}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* View Toggle */}
      <div className="flex space-x-2 space-x-reverse">
        <Button 
          variant={viewMode === 'list' ? 'default' : 'outline'}
          onClick={() => setViewMode('list')}
        >
          عرض القائمة
        </Button>
        <Button 
          variant={viewMode === 'calendar' ? 'default' : 'outline'}
          onClick={() => setViewMode('calendar')}
        >
          <CalendarIcon className="ml-2 h-4 w-4" />
          عرض التقويم
        </Button>
      </div>

      {viewMode === 'list' ? (
        /* List View */
        <Card>
          <CardHeader>
            <CardTitle>خطط الإنتاج</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {plans.map((plan) => (
                <div key={plan.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Badge className={getStatusColor(plan.status)}>
                        {getStatusText(plan.status)}
                      </Badge>
                      <Badge className={getPriorityColor(plan.priority)}>
                        {getPriorityText(plan.priority)}
                      </Badge>
                      <div>
                        <h3 className="font-semibold">{plan.productName}</h3>
                        <p className="text-sm text-gray-600">الكمية: {plan.quantity} قطعة</p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="text-sm text-gray-600">التقدم</p>
                      <p className="font-semibold">
                        {plan.completedHours}/{plan.estimatedHours} ساعة
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                      <p className="text-sm text-gray-600">تاريخ البداية</p>
                      <p className="font-medium">{plan.startDate.toLocaleDateString('ar-SA')}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">تاريخ النهاية</p>
                      <p className="font-medium">{plan.endDate.toLocaleDateString('ar-SA')}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">الفريق المكلف</p>
                      <p className="font-medium">{plan.assignedTeam}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">المواد المطلوبة</p>
                      <div className="flex flex-wrap gap-1">
                        {plan.materials.map((material, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {material}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">الماكينات المطلوبة</p>
                      <div className="flex flex-wrap gap-1">
                        {plan.machines.map((machine, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {machine}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-3 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${plan.estimatedHours > 0 ? (plan.completedHours / plan.estimatedHours) * 100 : 0}%` 
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Calendar View */
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>التقويم</CardTitle>
            </CardHeader>
            <CardContent>
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                className="rounded-md border"
              />
            </CardContent>
          </Card>

          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>
                خطط اليوم - {selectedDate?.toLocaleDateString('ar-SA')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {plans
                  .filter(plan => {
                    if (!selectedDate) return false
                    return plan.startDate <= selectedDate && plan.endDate >= selectedDate
                  })
                  .map((plan) => (
                    <div key={plan.id} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">{plan.productName}</h4>
                          <p className="text-sm text-gray-600">{plan.assignedTeam}</p>
                        </div>
                        <Badge className={getStatusColor(plan.status)}>
                          {getStatusText(plan.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                {plans.filter(plan => {
                  if (!selectedDate) return false
                  return plan.startDate <= selectedDate && plan.endDate >= selectedDate
                }).length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    لا توجد خطط مجدولة لهذا اليوم
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
