import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 بدء إضافة البيانات التجريبية...')

  // إنشاء المستخدمين
  const hashedPassword = await bcrypt.hash('admin123', 12)
  const hashedSuperPassword = await bcrypt.hash('super123', 12)
  const hashedOperPassword = await bcrypt.hash('oper123', 12)

  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      name: 'مدير النظام',
      password: hashedPassword,
      role: 'ADMIN',
      department: 'الإدارة العامة',
      isActive: true,
    },
  })

  const supervisor = await prisma.user.upsert({
    where: { username: 'supervisor' },
    update: {},
    create: {
      username: 'supervisor',
      email: '<EMAIL>',
      name: 'مشرف الإنتاج',
      password: hashedSuperPassword,
      role: 'SUPERVISOR',
      department: 'الإنتاج',
      isActive: true,
    },
  })

  const operator = await prisma.user.upsert({
    where: { username: 'operator' },
    update: {},
    create: {
      username: 'operator',
      email: '<EMAIL>',
      name: 'مشغل الماكينة',
      password: hashedOperPassword,
      role: 'OPERATOR',
      department: 'الإنتاج',
      isActive: true,
    },
  })

  // إنشاء الموردين
  const supplier1 = await prisma.supplier.create({
    data: {
      code: 'SUP001',
      nameAr: 'شركة القطن المصري',
      nameEn: 'Egyptian Cotton Company',
      contactPerson: 'أحمد محمد',
      phone: '+966501234567',
      email: '<EMAIL>',
      address: 'الرياض، المملكة العربية السعودية',
      isActive: true,
    },
  })

  const supplier2 = await prisma.supplier.create({
    data: {
      code: 'SUP002',
      nameAr: 'مصنع الألوان الحديث',
      nameEn: 'Modern Dyes Factory',
      contactPerson: 'سارة أحمد',
      phone: '+966502345678',
      email: '<EMAIL>',
      address: 'جدة، المملكة العربية السعودية',
      isActive: true,
    },
  })

  // إنشاء العملاء
  const customer1 = await prisma.customer.create({
    data: {
      code: 'CUS001',
      nameAr: 'متجر الأزياء الراقية',
      nameEn: 'Luxury Fashion Store',
      contactPerson: 'فاطمة علي',
      phone: '+966503456789',
      email: '<EMAIL>',
      address: 'الدمام، المملكة العربية السعودية',
      isActive: true,
    },
  })

  // إنشاء المواد الخام
  const cotton = await prisma.material.create({
    data: {
      code: 'MAT001',
      nameAr: 'قطن خام عالي الجودة',
      nameEn: 'High Quality Raw Cotton',
      type: 'RAW_MATERIAL',
      unit: 'كيلو',
      minStock: 1000,
      maxStock: 5000,
      currentStock: 2500,
      unitCost: 15.50,
      supplierId: supplier1.id,
      isActive: true,
    },
  })

  const blueDye = await prisma.material.create({
    data: {
      code: 'MAT002',
      nameAr: 'صبغة زرقاء',
      nameEn: 'Blue Dye',
      type: 'RAW_MATERIAL',
      unit: 'لتر',
      minStock: 100,
      maxStock: 500,
      currentStock: 250,
      unitCost: 45.00,
      supplierId: supplier2.id,
      isActive: true,
    },
  })

  const redDye = await prisma.material.create({
    data: {
      code: 'MAT003',
      nameAr: 'صبغة حمراء',
      nameEn: 'Red Dye',
      type: 'RAW_MATERIAL',
      unit: 'لتر',
      minStock: 100,
      maxStock: 500,
      currentStock: 180,
      unitCost: 42.00,
      supplierId: supplier2.id,
      isActive: true,
    },
  })

  // إنشاء المنتجات
  const blueShirt = await prisma.product.create({
    data: {
      code: 'PRD001',
      nameAr: 'قميص قطني أزرق',
      nameEn: 'Blue Cotton Shirt',
      description: 'قميص قطني عالي الجودة باللون الأزرق',
      category: 'قمصان',
      unit: 'قطعة',
      sellingPrice: 120.00,
      costPrice: 75.00,
      currentStock: 150,
      minStock: 50,
      isActive: true,
    },
  })

  const redShirt = await prisma.product.create({
    data: {
      code: 'PRD002',
      nameAr: 'قميص قطني أحمر',
      nameEn: 'Red Cotton Shirt',
      description: 'قميص قطني عالي الجودة باللون الأحمر',
      category: 'قمصان',
      unit: 'قطعة',
      sellingPrice: 125.00,
      costPrice: 78.00,
      currentStock: 120,
      minStock: 50,
      isActive: true,
    },
  })

  // إنشاء أوامر العمل
  const workOrder1 = await prisma.workOrder.create({
    data: {
      orderNumber: 'WO-2024-001',
      productId: blueShirt.id,
      quantity: 100,
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      startDate: new Date(),
      createdById: supervisor.id,
      assignedToId: operator.id,
      notes: 'أولوية عالية - طلب عاجل من العميل',
    },
  })

  // إنشاء عناصر أمر العمل
  await prisma.workOrderItem.create({
    data: {
      workOrderId: workOrder1.id,
      materialId: cotton.id,
      quantity: 50, // 50 كيلو قطن لإنتاج 100 قميص
      unitCost: 15.50,
    },
  })

  await prisma.workOrderItem.create({
    data: {
      workOrderId: workOrder1.id,
      materialId: blueDye.id,
      quantity: 10, // 10 لتر صبغة زرقاء
      unitCost: 45.00,
    },
  })

  // إنشاء مراحل الإنتاج
  await prisma.productionStage.createMany({
    data: [
      {
        workOrderId: workOrder1.id,
        stage: 'SPINNING',
        status: 'COMPLETED',
        startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // منذ يومين
        endTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // منذ يوم
        notes: 'تم الغزل بنجاح',
      },
      {
        workOrderId: workOrder1.id,
        stage: 'WEAVING',
        status: 'IN_PROGRESS',
        startTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // منذ يوم
        notes: 'النسيج قيد التنفيذ',
      },
      {
        workOrderId: workOrder1.id,
        stage: 'DYEING',
        status: 'PENDING',
        notes: 'في انتظار انتهاء النسيج',
      },
      {
        workOrderId: workOrder1.id,
        stage: 'FINISHING',
        status: 'PENDING',
        notes: 'في انتظار انتهاء الصباغة',
      },
    ],
  })

  // إنشاء أمر شراء
  const purchaseOrder1 = await prisma.purchaseOrder.create({
    data: {
      orderNumber: 'PO-2024-001',
      supplierId: supplier1.id,
      status: 'APPROVED',
      orderDate: new Date(),
      expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // خلال أسبوع
      totalAmount: 15500.00,
      createdById: admin.id,
      notes: 'طلب شراء قطن للإنتاج الشهري',
    },
  })

  await prisma.purchaseOrderItem.create({
    data: {
      purchaseOrderId: purchaseOrder1.id,
      materialId: cotton.id,
      quantity: 1000, // 1000 كيلو
      unitPrice: 15.50,
      totalPrice: 15500.00,
    },
  })

  // إنشاء أمر بيع
  const salesOrder1 = await prisma.salesOrder.create({
    data: {
      orderNumber: 'SO-2024-001',
      customerId: customer1.id,
      status: 'CONFIRMED',
      orderDate: new Date(),
      deliveryDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // خلال أسبوعين
      totalAmount: 12000.00,
      createdById: admin.id,
      notes: 'طلب من متجر الأزياء الراقية',
    },
  })

  await prisma.salesOrderItem.create({
    data: {
      salesOrderId: salesOrder1.id,
      productId: blueShirt.id,
      quantity: 100,
      unitPrice: 120.00,
      totalPrice: 12000.00,
    },
  })

  // إنشاء فحوصات الجودة
  await prisma.qualityCheck.create({
    data: {
      workOrderId: workOrder1.id,
      checkType: 'IN_PROCESS',
      status: 'PASSED',
      checkedById: supervisor.id,
      checkDate: new Date(),
      notes: 'جودة الغزل ممتازة',
      approved: true,
    },
  })

  // إنشاء حركات المخزون
  await prisma.inventoryMove.createMany({
    data: [
      {
        materialId: cotton.id,
        moveType: 'PURCHASE',
        quantity: 1000,
        unitCost: 15.50,
        reference: 'PO-2024-001',
        notes: 'استلام قطن من المورد',
        createdById: admin.id,
      },
      {
        materialId: cotton.id,
        moveType: 'OUT',
        quantity: -50,
        unitCost: 15.50,
        reference: 'WO-2024-001',
        notes: 'استخدام في أمر العمل',
        createdById: operator.id,
      },
      {
        materialId: blueDye.id,
        moveType: 'OUT',
        quantity: -10,
        unitCost: 45.00,
        reference: 'WO-2024-001',
        notes: 'استخدام في الصباغة',
        createdById: operator.id,
      },
    ],
  })

  console.log('✅ تم إنشاء البيانات التجريبية بنجاح!')
  console.log(`👤 المستخدمون: ${admin.name}, ${supervisor.name}, ${operator.name}`)
  console.log(`🏭 الموردون: ${supplier1.nameAr}, ${supplier2.nameAr}`)
  console.log(`🛒 العملاء: ${customer1.nameAr}`)
  console.log(`📦 المواد: ${cotton.nameAr}, ${blueDye.nameAr}, ${redDye.nameAr}`)
  console.log(`👕 المنتجات: ${blueShirt.nameAr}, ${redShirt.nameAr}`)
  console.log(`🔧 أوامر العمل: ${workOrder1.orderNumber}`)
  console.log(`💰 أوامر الشراء: ${purchaseOrder1.orderNumber}`)
  console.log(`📋 أوامر البيع: ${salesOrder1.orderNumber}`)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ خطأ في إنشاء البيانات:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
