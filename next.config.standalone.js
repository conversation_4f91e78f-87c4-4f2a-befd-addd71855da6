/** @type {import('next').NextConfig} */
const nextConfig = {
  // تصدير ثابت بدون خادم
  output: 'export',
  trailingSlash: true,
  distDir: 'out',

  // تعطيل الميزات التي تحتاج خادم
  images: {
    unoptimized: true
  },

  // إعدادات webpack للتطبيق المستقل
  webpack: (config, { isServer }) => {
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      'bufferutil': 'commonjs bufferutil',
    })

    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
      }
    }

    return config
  },

  // متغيرات البيئة للوضع المستقل
  env: {
    STANDALONE_MODE: 'true',
    DATABASE_URL: 'file:./data/database.db',
    OFFLINE_MODE: 'true'
  },

  // تعطيل التحسينات التي تحتاج خادم
  poweredByHeader: false,
  reactStrictMode: true
}

module.exports = nextConfig
