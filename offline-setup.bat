@echo off
chcp 65001 >nul
echo.
echo 🖥️  نظام الصفوة للنسيج - إعداد Windows Offline
echo ===============================================
echo.

echo [1/6] فحص متطلبات النظام...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً من ملف التثبيت المرفق
    pause
    exit /b 1
)

echo ✅ Node.js متاح

echo.
echo [2/6] إعداد قاعدة البيانات المحلية...
if not exist "data" mkdir data
if not exist "data\database.db" (
    echo 📊 إنشاء قاعدة بيانات SQLite محلية...
    cmd /c "npx prisma db push --schema=prisma/schema-offline.prisma"
    echo ✅ تم إنشاء قاعدة البيانات المحلية
) else (
    echo ✅ قاعدة البيانات المحلية موجودة
)

echo.
echo [3/6] تثبيت المكتبات المحلية...
if not exist "node_modules" (
    echo 📦 تثبيت المكتبات من الكاش المحلي...
    cmd /c "npm install --offline --prefer-offline"
    if %errorlevel% neq 0 (
        echo 📦 تثبيت من الملفات المحلية...
        cmd /c "npm install --no-optional"
    )
) else (
    echo ✅ المكتبات مثبتة
)

echo.
echo [4/6] إعداد الخطوط العربية المحلية...
if not exist "public\fonts" mkdir public\fonts
if not exist "public\fonts\cairo.woff2" (
    echo 📝 نسخ الخطوط العربية...
    copy "assets\fonts\*" "public\fonts\" >nul 2>&1
    echo ✅ تم نسخ الخطوط العربية
)

echo.
echo [5/6] إعداد ملف البيئة للوضع المحلي...
if not exist ".env.offline" (
    echo 📄 إنشاء إعدادات الوضع المحلي...
    echo DATABASE_URL="file:./data/database.db" > .env.offline
    echo NEXTAUTH_URL="http://localhost:3000" >> .env.offline
    echo NEXTAUTH_SECRET="safa-textile-offline-2024" >> .env.offline
    echo NODE_ENV="production" >> .env.offline
    echo OFFLINE_MODE="true" >> .env.offline
    echo ✅ تم إنشاء إعدادات الوضع المحلي
)

copy ".env.offline" ".env" >nul

echo.
echo [6/6] إضافة البيانات التجريبية...
cmd /c "npx prisma db seed"

echo.
echo ===============================================
echo ✅ تم إعداد النظام للعمل بدون إنترنت!
echo ===============================================
echo.
echo 🌐 النظام سيعمل على: http://localhost:3000
echo 📊 قاعدة البيانات: SQLite محلية
echo 🔒 وضع الأمان: محلي بدون إنترنت
echo.
echo 🔐 الحسابات التجريبية:
echo - مدير: <EMAIL> / admin123
echo - مشرف: <EMAIL> / super123
echo - مشغل: <EMAIL> / oper123
echo.
echo 🚀 تشغيل النظام...
cmd /c "npm run build"
cmd /c "npm start"
