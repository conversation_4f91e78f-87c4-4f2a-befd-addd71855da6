'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, AlertTriangle, Package } from 'lucide-react'

// بيانات تجريبية للمنتجات
const mockProducts = [
  {
    id: '1',
    code: 'PRD001',
    nameAr: 'قميص قطني أزرق',
    nameEn: 'Blue Cotton Shirt',
    description: 'قميص قطني عالي الجودة باللون الأزرق',
    category: 'قمصان',
    unit: 'قطعة',
    currentStock: 150,
    minStock: 50,
    sellingPrice: 120.00,
    costPrice: 75.00,
    isActive: true,
  },
  {
    id: '2',
    code: 'PRD002',
    nameAr: 'قميص قطني أحمر',
    nameEn: 'Red Cotton Shirt',
    description: 'قميص قطني عالي الجودة باللون الأحمر',
    category: 'قمصان',
    unit: 'قطعة',
    currentStock: 120,
    minStock: 50,
    sellingPrice: 125.00,
    costPrice: 78.00,
    isActive: true,
  },
  {
    id: '3',
    code: 'PRD003',
    nameAr: 'بنطال جينز أزرق',
    nameEn: 'Blue Jeans',
    description: 'بنطال جينز عالي الجودة',
    category: 'بناطيل',
    unit: 'قطعة',
    currentStock: 30,
    minStock: 40,
    sellingPrice: 180.00,
    costPrice: 110.00,
    isActive: true,
  },
  {
    id: '4',
    code: 'PRD004',
    nameAr: 'فستان صيفي',
    nameEn: 'Summer Dress',
    description: 'فستان صيفي خفيف ومريح',
    category: 'فساتين',
    unit: 'قطعة',
    currentStock: 85,
    minStock: 30,
    sellingPrice: 200.00,
    costPrice: 130.00,
    isActive: true,
  },
]

export function ProductsList() {
  const [products] = useState(mockProducts)

  const getStockStatus = (current: number, min: number) => {
    if (current <= min * 0.5) {
      return { status: 'critical', label: 'حرج', color: 'bg-red-100 text-red-800' }
    } else if (current <= min) {
      return { status: 'low', label: 'منخفض', color: 'bg-yellow-100 text-yellow-800' }
    } else {
      return { status: 'normal', label: 'طبيعي', color: 'bg-green-100 text-green-800' }
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'قمصان':
        return 'bg-blue-100 text-blue-800'
      case 'بناطيل':
        return 'bg-purple-100 text-purple-800'
      case 'فساتين':
        return 'bg-pink-100 text-pink-800'
      case 'ملابس داخلية':
        return 'bg-orange-100 text-orange-800'
      case 'إكسسوارات':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getMarginPercentage = (selling: number, cost: number) => {
    return ((selling - cost) / selling * 100).toFixed(1)
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الكود</TableHead>
            <TableHead>اسم المنتج</TableHead>
            <TableHead>الفئة</TableHead>
            <TableHead>المخزون الحالي</TableHead>
            <TableHead>حالة المخزون</TableHead>
            <TableHead>سعر البيع</TableHead>
            <TableHead>سعر التكلفة</TableHead>
            <TableHead>هامش الربح</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {products.map((product) => {
            const stockStatus = getStockStatus(product.currentStock, product.minStock)
            const marginPercentage = getMarginPercentage(product.sellingPrice, product.costPrice)
            
            return (
              <TableRow key={product.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    {product.code}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{product.nameAr}</div>
                    {product.nameEn && (
                      <div className="text-sm text-muted-foreground">
                        {product.nameEn}
                      </div>
                    )}
                    {product.description && (
                      <div className="text-xs text-muted-foreground mt-1">
                        {product.description}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getCategoryColor(product.category)}>
                    {product.category}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {formatNumber(product.currentStock)} {product.unit}
                    {stockStatus.status === 'critical' && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    الحد الأدنى: {formatNumber(product.minStock)} {product.unit}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={stockStatus.color}>
                    {stockStatus.label}
                  </Badge>
                </TableCell>
                <TableCell className="font-medium">
                  {formatCurrency(product.sellingPrice)}
                </TableCell>
                <TableCell>
                  {formatCurrency(product.costPrice)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <span className="font-medium text-green-600">
                      {marginPercentage}%
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrency(product.sellingPrice - product.costPrice)} ربح
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={product.isActive ? 'default' : 'secondary'}>
                    {product.isActive ? 'نشط' : 'غير نشط'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="ml-2 h-4 w-4" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="ml-2 h-4 w-4" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="ml-2 h-4 w-4" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
