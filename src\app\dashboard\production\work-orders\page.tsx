import { Suspense } from 'react'
import { WorkOrdersList } from '@/components/production/work-orders-list'
import { WorkOrdersHeader } from '@/components/production/work-orders-header'
import { WorkOrdersStats } from '@/components/production/work-orders-stats'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function WorkOrdersPage() {
  return (
    <div className="space-y-6">
      <WorkOrdersHeader />
      
      <Suspense fallback={<StatsSkeleton />}>
        <WorkOrdersStats />
      </Suspense>
      
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<WorkOrdersListSkeleton />}>
            <WorkOrdersList />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}

function StatsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function WorkOrdersListSkeleton() {
  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        ))}
      </div>
    </div>
  )
}
