'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Calendar } from '@/components/ui/calendar'
import { 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  Calendar as CalendarIcon,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Plus
} from 'lucide-react'

interface AttendanceRecord {
  id: string
  employeeId: string
  employeeName: string
  department: string
  date: Date
  checkIn?: Date
  checkOut?: Date
  workingHours: number
  overtimeHours: number
  status: string
  notes?: string
  lateMinutes: number
  earlyLeaveMinutes: number
}

const mockAttendance: AttendanceRecord[] = [
  {
    id: '1',
    employeeId: 'EMP001',
    employeeName: 'أحمد محمد',
    department: 'الإنتاج',
    date: new Date('2024-01-15'),
    checkIn: new Date('2024-01-15T08:00:00'),
    checkOut: new Date('2024-01-15T17:00:00'),
    workingHours: 9,
    overtimeHours: 1,
    status: 'PRESENT',
    lateMinutes: 0,
    earlyLeaveMinutes: 0
  },
  {
    id: '2',
    employeeId: 'EMP002',
    employeeName: 'فاطمة علي',
    department: 'الجودة',
    date: new Date('2024-01-15'),
    checkIn: new Date('2024-01-15T08:15:00'),
    checkOut: new Date('2024-01-15T16:45:00'),
    workingHours: 8.5,
    overtimeHours: 0,
    status: 'LATE',
    lateMinutes: 15,
    earlyLeaveMinutes: 15,
    notes: 'تأخر بسبب ظروف المواصلات'
  },
  {
    id: '3',
    employeeId: 'EMP003',
    employeeName: 'محمد أحمد',
    department: 'الصيانة',
    date: new Date('2024-01-15'),
    checkIn: new Date('2024-01-15T07:45:00'),
    checkOut: new Date('2024-01-15T18:30:00'),
    workingHours: 10.75,
    overtimeHours: 2.75,
    status: 'OVERTIME',
    lateMinutes: 0,
    earlyLeaveMinutes: 0,
    notes: 'عمل إضافي لصيانة الماكينات'
  },
  {
    id: '4',
    employeeId: 'EMP004',
    employeeName: 'سارة محمود',
    department: 'المبيعات',
    date: new Date('2024-01-15'),
    workingHours: 0,
    overtimeHours: 0,
    status: 'ABSENT',
    lateMinutes: 0,
    earlyLeaveMinutes: 0,
    notes: 'إجازة مرضية'
  },
  {
    id: '5',
    employeeId: 'EMP005',
    employeeName: 'خالد عبدالله',
    department: 'المخزون',
    date: new Date('2024-01-15'),
    checkIn: new Date('2024-01-15T08:00:00'),
    workingHours: 4,
    overtimeHours: 0,
    status: 'HALF_DAY',
    lateMinutes: 0,
    earlyLeaveMinutes: 0,
    notes: 'نصف يوم - ظروف شخصية'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PRESENT': return 'bg-green-500'
    case 'LATE': return 'bg-yellow-500'
    case 'ABSENT': return 'bg-red-500'
    case 'OVERTIME': return 'bg-blue-500'
    case 'HALF_DAY': return 'bg-orange-500'
    case 'LEAVE': return 'bg-purple-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PRESENT': return 'حاضر'
    case 'LATE': return 'متأخر'
    case 'ABSENT': return 'غائب'
    case 'OVERTIME': return 'عمل إضافي'
    case 'HALF_DAY': return 'نصف يوم'
    case 'LEAVE': return 'إجازة'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'PRESENT': return <CheckCircle className="h-4 w-4" />
    case 'LATE': return <AlertTriangle className="h-4 w-4" />
    case 'ABSENT': return <XCircle className="h-4 w-4" />
    case 'OVERTIME': return <Clock className="h-4 w-4" />
    case 'HALF_DAY': return <Clock className="h-4 w-4" />
    case 'LEAVE': return <CalendarIcon className="h-4 w-4" />
    default: return <Clock className="h-4 w-4" />
  }
}

export default function AttendancePage() {
  const [attendance, setAttendance] = useState<AttendanceRecord[]>(mockAttendance)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all')
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date('2024-01-15'))

  const filteredAttendance = attendance.filter(record => {
    const matchesSearch = record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || record.status === selectedStatus
    const matchesDepartment = selectedDepartment === 'all' || record.department === selectedDepartment
    const matchesDate = !selectedDate || record.date.toDateString() === selectedDate.toDateString()
    return matchesSearch && matchesStatus && matchesDepartment && matchesDate
  })

  const departments = Array.from(new Set(attendance.map(record => record.department)))

  const totalPresent = attendance.filter(r => r.status === 'PRESENT' || r.status === 'LATE' || r.status === 'OVERTIME' || r.status === 'HALF_DAY').length
  const totalAbsent = attendance.filter(r => r.status === 'ABSENT').length
  const totalLate = attendance.filter(r => r.status === 'LATE').length
  const totalOvertime = attendance.reduce((acc, r) => acc + r.overtimeHours, 0)

  const handleMarkPresent = (recordId: string) => {
    setAttendance(attendance.map(record => 
      record.id === recordId 
        ? { 
            ...record, 
            status: 'PRESENT',
            checkIn: new Date(),
            workingHours: 8
          }
        : record
    ))
  }

  const handleMarkAbsent = (recordId: string) => {
    setAttendance(attendance.map(record => 
      record.id === recordId 
        ? { 
            ...record, 
            status: 'ABSENT',
            checkIn: undefined,
            checkOut: undefined,
            workingHours: 0
          }
        : record
    ))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الحضور والانصراف</h1>
          <p className="text-gray-600">متابعة حضور وانصراف الموظفين</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير التقرير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            تسجيل حضور
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الحاضرين</p>
                <p className="text-2xl font-bold text-green-600">{totalPresent}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الغائبين</p>
                <p className="text-2xl font-bold text-red-600">{totalAbsent}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المتأخرين</p>
                <p className="text-2xl font-bold text-yellow-600">{totalLate}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">ساعات إضافية</p>
                <p className="text-2xl font-bold text-blue-600">{totalOvertime.toFixed(1)}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Date Picker and Filters */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>اختيار التاريخ</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>تصفية البيانات</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث عن موظف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="PRESENT">حاضر</option>
                <option value="LATE">متأخر</option>
                <option value="ABSENT">غائب</option>
                <option value="OVERTIME">عمل إضافي</option>
                <option value="HALF_DAY">نصف يوم</option>
                <option value="LEAVE">إجازة</option>
              </select>
              
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Records */}
      <Card>
        <CardHeader>
          <CardTitle>
            سجل الحضور - {selectedDate?.toLocaleDateString('ar-SA')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredAttendance.map((record) => (
              <div key={record.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(record.status)}>
                      {getStatusIcon(record.status)}
                      <span className="mr-1">{getStatusText(record.status)}</span>
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{record.employeeName}</h3>
                      <p className="text-sm text-gray-600">
                        {record.employeeId} - {record.department}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {record.status === 'ABSENT' && (
                      <Button 
                        size="sm"
                        onClick={() => handleMarkPresent(record.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 ml-1" />
                        تسجيل حضور
                      </Button>
                    )}
                    {record.status !== 'ABSENT' && (
                      <Button 
                        size="sm"
                        variant="outline"
                        onClick={() => handleMarkAbsent(record.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <XCircle className="h-4 w-4 ml-1" />
                        تسجيل غياب
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">وقت الحضور</p>
                    <p className="font-medium">
                      {record.checkIn ? record.checkIn.toLocaleTimeString('ar-SA') : '-'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">وقت الانصراف</p>
                    <p className="font-medium">
                      {record.checkOut ? record.checkOut.toLocaleTimeString('ar-SA') : '-'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">ساعات العمل</p>
                    <p className="font-medium">{record.workingHours} ساعة</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">ساعات إضافية</p>
                    <p className="font-medium text-blue-600">{record.overtimeHours} ساعة</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">التأخير</p>
                    <p className={`font-medium ${record.lateMinutes > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {record.lateMinutes > 0 ? `${record.lateMinutes} دقيقة` : 'لا يوجد'}
                    </p>
                  </div>
                </div>

                {record.earlyLeaveMinutes > 0 && (
                  <div className="mb-3 p-2 bg-yellow-100 rounded text-sm">
                    <strong>انصراف مبكر:</strong> {record.earlyLeaveMinutes} دقيقة
                  </div>
                )}

                {record.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {record.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredAttendance.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد سجلات حضور تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
