'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

const productionData = [
  {
    day: 'السبت',
    planned: 100,
    actual: 95,
  },
  {
    day: 'الأحد',
    planned: 120,
    actual: 118,
  },
  {
    day: 'الاثنين',
    planned: 110,
    actual: 105,
  },
  {
    day: 'الثلاثاء',
    planned: 130,
    actual: 125,
  },
  {
    day: 'الأربعاء',
    planned: 115,
    actual: 112,
  },
  {
    day: 'الخميس',
    planned: 125,
    actual: 120,
  },
  {
    day: 'الجمعة',
    planned: 90,
    actual: 88,
  },
]

export function ProductionChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>الإنتاج الأسبوعي</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={productionData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="day" 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '6px',
                  fontSize: '12px',
                }}
                labelStyle={{ color: 'hsl(var(--foreground))' }}
              />
              <Bar 
                dataKey="planned" 
                fill="hsl(var(--muted))" 
                name="المخطط"
                radius={[2, 2, 0, 0]}
              />
              <Bar 
                dataKey="actual" 
                fill="hsl(var(--primary))" 
                name="الفعلي"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        <div className="flex items-center justify-center gap-6 mt-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-muted rounded"></div>
            <span className="text-sm text-muted-foreground">الإنتاج المخطط</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded"></div>
            <span className="text-sm text-muted-foreground">الإنتاج الفعلي</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
