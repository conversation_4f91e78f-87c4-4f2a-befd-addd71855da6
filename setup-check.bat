@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo 🔍 فحص إعداد نظام الصفوة للنسيج
echo ===================================
echo.

REM فحص Node.js
echo [1/4] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js مثبت: !NODE_VERSION!
    set NODE_OK=1
) else (
    echo ❌ Node.js غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 الحلول المقترحة:
    echo 1. تأكد من تثبيت Node.js من https://nodejs.org/
    echo 2. أعد تشغيل Command Prompt بعد التثبيت
    echo 3. أعد تشغيل الكمبيوتر إذا لزم الأمر
    echo.
    set NODE_OK=0
)

echo.

REM فحص npm
echo [2/4] فحص npm...
if !NODE_OK!==1 (
    npm --version >nul 2>&1
    if %errorlevel% equ 0 (
        for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
        echo ✅ npm مثبت: !NPM_VERSION!
        set NPM_OK=1
    ) else (
        echo ❌ npm غير متاح
        set NPM_OK=0
    )
) else (
    echo ⏭️  تم تخطي فحص npm (Node.js غير متاح)
    set NPM_OK=0
)

echo.

REM فحص PostgreSQL
echo [3/4] فحص PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('psql --version') do set PSQL_VERSION=%%i
    echo ✅ PostgreSQL مثبت: !PSQL_VERSION!
    set PSQL_OK=1
) else (
    echo ⚠️  PostgreSQL غير مثبت أو غير موجود في PATH
    echo.
    echo 🔧 الحلول المقترحة:
    echo 1. تأكد من تثبيت PostgreSQL من https://www.postgresql.org/download/windows/
    echo 2. أضف PostgreSQL إلى PATH: C:\Program Files\PostgreSQL\16\bin
    echo 3. أعد تشغيل Command Prompt
    echo.
    echo ℹ️  يمكن المتابعة بدون PostgreSQL مؤقتاً
    set PSQL_OK=0
)

echo.

REM فحص ملفات المشروع
echo [4/4] فحص ملفات المشروع...
if exist "package.json" (
    echo ✅ ملف package.json موجود
    set PROJECT_OK=1
) else (
    echo ❌ ملف package.json غير موجود
    echo تأكد من أنك في مجلد المشروع الصحيح
    set PROJECT_OK=0
)

if exist "src" (
    echo ✅ مجلد src موجود
) else (
    echo ❌ مجلد src غير موجود
)

if exist ".env.example" (
    echo ✅ ملف .env.example موجود
) else (
    echo ❌ ملف .env.example غير موجود
)

echo.
echo ===================================
echo 📊 ملخص الفحص:
echo ===================================

if !NODE_OK!==1 (
    echo ✅ Node.js: جاهز
) else (
    echo ❌ Node.js: غير جاهز
)

if !NPM_OK!==1 (
    echo ✅ npm: جاهز
) else (
    echo ❌ npm: غير جاهز
)

if !PSQL_OK!==1 (
    echo ✅ PostgreSQL: جاهز
) else (
    echo ⚠️  PostgreSQL: غير جاهز (اختياري)
)

if !PROJECT_OK!==1 (
    echo ✅ ملفات المشروع: جاهزة
) else (
    echo ❌ ملفات المشروع: غير جاهزة
)

echo.

REM تحديد الخطوات التالية
if !NODE_OK!==1 if !NPM_OK!==1 if !PROJECT_OK!==1 (
    echo 🎉 النظام جاهز للتشغيل!
    echo.
    echo 🚀 الخطوات التالية:
    echo 1. تثبيت المكتبات: npm install
    echo 2. إنشاء ملف البيئة: copy .env.example .env
    echo 3. تشغيل النظام: npm run dev
    echo.
    echo هل تريد تشغيل هذه الخطوات تلقائياً؟ (y/n)
    set /p autoRun=
    if "!autoRun!"=="y" (
        echo.
        echo 📦 تثبيت المكتبات...
        npm install
        
        if %errorlevel% equ 0 (
            echo ✅ تم تثبيت المكتبات بنجاح
            
            echo.
            echo 📄 إنشاء ملف البيئة...
            if not exist ".env" (
                copy ".env.example" ".env" >nul
                echo ✅ تم إنشاء ملف .env
            ) else (
                echo ℹ️  ملف .env موجود بالفعل
            )
            
            echo.
            echo 🚀 تشغيل النظام...
            echo.
            echo =====================================
            echo 🌐 النظام سيعمل على: http://localhost:3000
            echo =====================================
            echo.
            echo 🔐 الحسابات التجريبية:
            echo - مدير: <EMAIL> / admin123
            echo - مشرف: <EMAIL> / super123
            echo - مشغل: <EMAIL> / oper123
            echo.
            echo ⏹️  لإيقاف النظام: اضغط Ctrl+C
            echo.
            
            npm run dev
        ) else (
            echo ❌ فشل في تثبيت المكتبات
        )
    )
) else (
    echo ❌ النظام غير جاهز للتشغيل
    echo.
    echo 🔧 يرجى إكمال التثبيت أولاً:
    if !NODE_OK!==0 (
        echo - تثبيت Node.js من https://nodejs.org/
    )
    if !PSQL_OK!==0 (
        echo - تثبيت PostgreSQL من https://www.postgresql.org/download/windows/
    )
    if !PROJECT_OK!==0 (
        echo - التأكد من وجود ملفات المشروع
    )
)

echo.
pause
