/* تصميم عصري لشركة الصفوة */

@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&family=Tajawal:wght@200;300;400;500;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
  /* ألوان عصرية لشركة الصفوة */
  --safa-primary: #2563eb;
  --safa-primary-dark: #1d4ed8;
  --safa-primary-light: #3b82f6;
  --safa-secondary: #06b6d4;
  --safa-accent: #8b5cf6;
  --safa-success: #10b981;
  --safa-warning: #f59e0b;
  --safa-error: #ef4444;
  
  /* متدرجات عصرية */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  --gradient-accent: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  
  /* ظلال عصرية */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* تأثيرات الإضاءة */
  --glow-primary: 0 0 20px rgba(37, 99, 235, 0.3);
  --glow-secondary: 0 0 20px rgba(6, 182, 212, 0.3);
  --glow-accent: 0 0 20px rgba(139, 92, 246, 0.3);
}

/* تحسينات الخطوط */
.font-modern {
  font-family: 'Inter', 'Cairo', 'Tajawal', sans-serif;
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* بطاقات عصرية */
.modern-card {
  background: var(--gradient-card);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

/* أزرار عصرية */
.modern-btn {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.modern-btn-primary {
  background: var(--gradient-primary);
  color: white;
  border: none;
  box-shadow: var(--shadow-md);
}

.modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-primary);
}

.modern-btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  border: none;
  box-shadow: var(--shadow-md);
}

.modern-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), var(--glow-secondary);
}

/* تأثير الموجة للأزرار */
.modern-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.modern-btn:active::before {
  width: 300px;
  height: 300px;
}

.modern-btn > * {
  position: relative;
  z-index: 1;
}

/* حقول الإدخال العصرية */
.modern-input {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(37, 99, 235, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.modern-input:focus {
  border-color: var(--safa-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  outline: none;
}

/* شريط جانبي عصري */
.modern-sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid rgba(37, 99, 235, 0.1);
  backdrop-filter: blur(20px);
}

.modern-sidebar-item {
  border-radius: 12px;
  margin: 4px 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-sidebar-item:hover {
  background: rgba(37, 99, 235, 0.05);
  transform: translateX(4px);
}

.modern-sidebar-item.active {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.modern-sidebar-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: white;
  border-radius: 0 4px 4px 0;
}

/* رأس الصفحة العصري */
.modern-header {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(37, 99, 235, 0.1);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-sm);
}

/* جداول عصرية */
.modern-table {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(37, 99, 235, 0.1);
}

.modern-table thead {
  background: var(--gradient-primary);
  color: white;
}

.modern-table tbody tr {
  transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
  background: rgba(37, 99, 235, 0.02);
  transform: scale(1.01);
}

/* شارات عصرية */
.modern-badge {
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.modern-badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.modern-badge-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.modern-badge-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.modern-badge-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
}

/* إحصائيات عصرية */
.modern-stat-card {
  background: var(--gradient-card);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.modern-stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

/* أيقونات عصرية */
.modern-icon {
  padding: 12px;
  border-radius: 12px;
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.modern-icon:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg), var(--glow-primary);
}

/* تحسينات الرسوم المتحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .modern-card {
    border-radius: 12px;
    margin: 8px;
  }
  
  .modern-stat-card {
    padding: 16px;
    border-radius: 16px;
  }
  
  .modern-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}
