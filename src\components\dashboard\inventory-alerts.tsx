import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, Package } from 'lucide-react'

const inventoryAlerts = [
  {
    id: 'MAT001',
    name: 'قطن خام عالي الجودة',
    currentStock: 150,
    minStock: 1000,
    unit: 'كيلو',
    alertType: 'low_stock',
    alertText: 'مخزون منخفض',
  },
  {
    id: 'MAT003',
    name: 'صبغة حمراء',
    currentStock: 25,
    minStock: 100,
    unit: 'لتر',
    alertType: 'critical',
    alertText: 'مخزون حرج',
  },
  {
    id: 'PRD001',
    name: 'قميص قطني أزرق',
    currentStock: 30,
    minStock: 50,
    unit: 'قطعة',
    alertType: 'low_stock',
    alertText: 'مخزون منخفض',
  },
]

export function InventoryAlerts() {
  const getAlertColor = (alertType: string) => {
    switch (alertType) {
      case 'critical':
        return 'bg-red-100 text-red-800'
      case 'low_stock':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'low_stock':
        return <Package className="h-4 w-4 text-yellow-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          تنبيهات المخزون
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {inventoryAlerts.map((alert) => (
            <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg bg-yellow-50">
              <div className="flex items-center gap-3">
                {getAlertIcon(alert.alertType)}
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-sm">{alert.name}</span>
                    <span className="text-xs text-muted-foreground">({alert.id})</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    المخزون الحالي: {alert.currentStock} {alert.unit}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    الحد الأدنى: {alert.minStock} {alert.unit}
                  </p>
                </div>
              </div>
              <Badge className={getAlertColor(alert.alertType)}>
                {alert.alertText}
              </Badge>
            </div>
          ))}
        </div>
        
        {inventoryAlerts.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>لا توجد تنبيهات مخزون حالياً</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
