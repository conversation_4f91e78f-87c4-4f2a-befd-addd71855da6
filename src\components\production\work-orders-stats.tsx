import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Clock, Play, CheckCircle, AlertTriangle } from 'lucide-react'

const stats = [
  {
    title: 'في الانتظار',
    value: '8',
    change: '+2',
    changeType: 'neutral' as const,
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    title: 'قيد التنفيذ',
    value: '12',
    change: '+3',
    changeType: 'positive' as const,
    icon: Play,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    title: 'مكتملة',
    value: '45',
    change: '+8',
    changeType: 'positive' as const,
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    title: 'متأخرة',
    value: '3',
    change: '-1',
    changeType: 'negative' as const,
    icon: AlertTriangle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
  },
]

export function WorkOrdersStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-full ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : stat.changeType === 'negative'
                ? 'text-red-600'
                : 'text-muted-foreground'
            }`}>
              {stat.change} من الأسبوع الماضي
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
