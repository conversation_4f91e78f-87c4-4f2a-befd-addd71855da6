@echo off
title إنشاء برنامج نظام الصفوة الحقيقي - مبسط
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء برنامج نظام الصفوة الحقيقي - مبسط
echo                              🖥️ برنامج Windows حقيقي بنقرة واحدة 🖥️
echo.
echo ===============================================================================
echo.

echo مرحباً بك في منشئ برنامج نظام الصفوة الحقيقي!
echo.
echo هذا المنشئ سيحول النظام إلى برنامج Windows حقيقي يعمل مثل:
echo • Microsoft Office
echo • Adobe Photoshop  
echo • أي برنامج Windows عادي
echo.
echo المميزات:
echo ✓ لا يحتاج Node.js أو أي متطلبات
echo ✓ لا يحتاج إنترنت تماماً
echo ✓ مثبت احترافي مع اختصارات
echo ✓ إلغاء تثبيت من لوحة التحكم
echo ✓ يعمل على أي جهاز Windows
echo.
echo هل تريد المتابعة؟ (Y/N)
set /p confirm="اكتب Y للمتابعة أو N للإلغاء: "

if /i "%confirm%" neq "Y" (
    echo تم إلغاء العملية.
    goto end
)

echo.
echo ===============================================
echo بدء إنشاء البرنامج الحقيقي
echo ===============================================
echo.

echo [1/10] إيقاف العمليات السابقة...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM electron.exe 2>nul

echo [2/10] تحضير البيئة...
set NODE_ENV=production

echo [3/10] تثبيت أدوات البناء...
echo تثبيت Electron Builder...
npm install electron-builder@latest --save-dev >nul 2>&1
npm install electron@latest --save-dev >nul 2>&1

echo [4/10] إنشاء مجلدات البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"

echo [5/10] تحضير الأيقونة...
if not exist "build\icon.ico" (
    copy "public\favicon.ico" "build\icon.ico" >nul 2>&1
)

echo [6/10] إنشاء إعدادات البناء...
(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لإدارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": "شركة الصفوة للنسيج",
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج"
echo     }
echo   }
echo }
) > package-build.json

echo [7/10] تحضير Next.js للبناء الثابت...
if exist next.config.js copy next.config.js next.config.js.backup >nul 2>&1
(
echo /** @type {import('next').NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   }
echo }
echo module.exports = nextConfig
) > next.config.js

echo [8/10] بناء ملفات Next.js...
echo جاري بناء الملفات الثابتة...
npm run build >nul 2>&1

echo [9/10] بناء البرنامج النهائي...
echo جاري إنشاء برنامج Windows...
copy package-build.json package.json >nul 2>&1
npm run dist >nul 2>&1

echo [10/10] التحقق من النتيجة...
if exist "dist" (
    echo.
    echo ===============================================
    echo ✓ تم إنشاء البرنامج الحقيقي بنجاح!
    echo ===============================================
    echo.
    echo الملفات المُنشأة:
    for /r dist %%i in (*.exe) do (
        echo 📦 ملف المثبت: %%~nxi
        echo 📊 الحجم: %%~zi bytes
        echo 📁 المسار: %%i
        echo.
    )
    echo.
    echo ✨ المميزات:
    echo ✓ برنامج Windows حقيقي
    echo ✓ يعمل بدون Node.js
    echo ✓ يعمل بدون إنترنت
    echo ✓ مثبت احترافي
    echo ✓ اختصارات تلقائية
    echo ✓ إلغاء تثبيت آمن
    echo ✓ جميع وحدات النسيج مدمجة
    echo.
    echo 🚀 طريقة الاستخدام:
    echo 1. انسخ ملف المثبت للجهاز المطلوب
    echo 2. شغل الملف واتبع التعليمات
    echo 3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
    echo 4. سجل دخول: admin / admin123
    echo.
    echo 🎊 مبروك! البرنامج جاهز للتوزيع والاستخدام!
) else (
    echo.
    echo ===============================================
    echo ✗ فشل في إنشاء البرنامج
    echo ===============================================
    echo.
    echo الأسباب المحتملة:
    echo • نقص في مساحة القرص
    echo • مكافح الفيروسات يحجب العملية
    echo • نقص في الصلاحيات
    echo • خطأ في إعدادات البناء
    echo.
    echo الحلول:
    echo 1. شغل Command Prompt كمدير
    echo 2. أغلق مكافح الفيروسات مؤقتاً
    echo 3. تأكد من مساحة القرص الكافية (5GB+)
    echo 4. أعد المحاولة
)

echo.
echo تنظيف الملفات المؤقتة...
if exist next.config.js.backup (
    copy next.config.js.backup next.config.js >nul 2>&1
    del next.config.js.backup >nul 2>&1
)
if exist package-build.json del package-build.json >nul 2>&1

echo.
echo ===============================================
echo اكتمل إنشاء البرنامج!
echo ===============================================
echo.
echo يمكنك الآن:
echo • توزيع ملف المثبت على أي جهاز Windows
echo • تثبيت البرنامج مثل أي برنامج عادي
echo • تشغيل البرنامج من قائمة ابدأ أو سطح المكتب
echo • استخدام جميع وحدات النسيج بدون إنترنت
echo.
echo البرنامج يعمل الآن مثل أي برنامج Windows حقيقي!
echo.

:end
pause
