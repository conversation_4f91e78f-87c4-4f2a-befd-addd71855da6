@echo off
title إنشاء مثبت نظام الصفوة EXE
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           إنشاء مثبت نظام الصفوة EXE
echo                              📦 ملف EXE واحد للتثبيت 📦
echo.
echo ===============================================================================
echo.

echo مرحباً بك في منشئ مثبت نظام الصفوة EXE!
echo.
echo هذا المنشئ سيقوم بإنشاء ملف EXE واحد يحتوي على:
echo ✓ مثبت نظام الصفوة الكامل
echo ✓ جميع الملفات والمكتبات المطلوبة
echo ✓ واجهة تثبيت احترافية
echo ✓ لا يحتاج أي ملفات إضافية
echo.

echo [1/8] تحضير البيئة...
set INSTALLER_DIR=SafaTextileInstaller
if exist "%INSTALLER_DIR%" rmdir /s /q "%INSTALLER_DIR%"
mkdir "%INSTALLER_DIR%"

echo [2/8] تحميل أداة تحويل BAT إلى EXE...
echo تحميل Bat To Exe Converter...

REM إنشاء ملف PowerShell لتحميل الأداة
(
echo $url = "https://github.com/islamadel/bat2exe/releases/download/v1.0/Bat_To_Exe_Converter.zip"
echo $output = "Bat_To_Exe_Converter.zip"
echo try {
echo     Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
echo     Write-Host "تم تحميل الأداة بنجاح"
echo } catch {
echo     Write-Host "فشل في تحميل الأداة، سيتم استخدام طريقة بديلة"
echo }
) > download_converter.ps1

powershell -ExecutionPolicy Bypass -File download_converter.ps1 2>nul

echo [3/8] إنشاء ملف التثبيت المحسن...
(
echo @echo off
echo title مثبت نظام الصفوة للنسيج
echo color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           مثبت نظام الصفوة للنسيج
echo                              📦 مثبت احترافي شامل 📦
echo.
echo ===============================================================================
echo.
echo مرحباً بك في مثبت نظام الصفوة لإدارة مصانع النسيج!
echo.
echo معلومات البرنامج:
echo • الاسم: نظام الصفوة لإدارة مصانع النسيج
echo • الإصدار: 1.0.0
echo • المطور: شركة الصفوة للنسيج
echo • النوع: نظام ERP متكامل
echo.
echo الوحدات المتاحة:
echo ✓ الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo ✓ المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo ✓ المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo ✓ المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo ✓ المالية: الحسابات، الفواتير، التقارير المالية
echo ✓ الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo ✓ أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo المميزات:
echo ✓ يعمل بدون إنترنت تماماً
echo ✓ واجهة عربية احترافية
echo ✓ شعار الصفوة في جميع الصفحات
echo ✓ بيانات تجريبية واقعية
echo ✓ أداء فائق وسرعة عالية
echo.
echo هل تريد المتابعة مع التثبيت؟ ^(Y/N^)
echo.
set /p confirm="اكتب Y للمتابعة أو N للإلغاء: "
echo.
if /i "%%confirm%%" neq "Y" ^(
    echo تم إلغاء التثبيت.
    pause
    exit /b 0
^)
echo.
echo ===============================================
echo بدء تثبيت نظام الصفوة للنسيج
echo ===============================================
echo.
echo [1/10] فحص متطلبات النظام...
echo فحص نظام التشغيل...
ver ^| find "Windows" ^>nul
if errorlevel 1 ^(
    echo ✗ هذا البرنامج يعمل على Windows فقط
    pause
    exit /b 1
^)
echo ✓ نظام التشغيل متوافق
echo.
echo فحص Node.js...
node --version ^>nul 2^>^&1
if errorlevel 1 ^(
    echo ✗ Node.js غير مثبت
    echo.
    echo يرجى تثبيت Node.js أولاً من:
    echo https://nodejs.org
    echo.
    echo بعد التثبيت، أعد تشغيل هذا المثبت.
    pause
    exit /b 1
^)
echo ✓ Node.js متوفر
echo.
echo [2/10] تحضير البيئة...
taskkill /F /IM node.exe 2^>nul
taskkill /F /IM electron.exe 2^>nul
set NODE_ENV=production
echo.
echo [3/10] تثبيت أدوات البناء...
echo تثبيت Electron Builder...
call npm install electron-builder@latest --save-dev
call npm install electron@latest --save-dev
echo.
echo [4/10] إنشاء مجلدات البناء...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"
echo.
echo [5/10] تحضير الأيقونة والموارد...
if not exist "build\icon.ico" ^(
    if exist "public\favicon.ico" ^(
        copy "public\favicon.ico" "build\icon.ico"
    ^)
^)
echo.
echo [6/10] إنشاء إعدادات البناء...
^(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "نظام الصفوة لإدارة مصانع النسيج",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": {
echo     "name": "شركة الصفوة للنسيج",
echo     "email": "<EMAIL>"
echo   },
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "نظام الصفوة للنسيج",
echo     "copyright": "© 2024 شركة الصفوة للنسيج",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "نظام الصفوة للنسيج",
echo       "artifactName": "نظام-الصفوة-للنسيج-${version}.${ext}"
echo     }
echo   }
echo }
^) ^> package-installer.json
echo.
echo [7/10] تحضير Next.js للبناء...
if exist next.config.js copy next.config.js next.config.js.backup
^(
echo /** @type {import^('next'^).NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   }
echo }
echo module.exports = nextConfig
^) ^> next.config.js
echo.
echo [8/10] بناء ملفات النظام...
echo جاري بناء الملفات الثابتة...
call npm run build
echo.
echo [9/10] إنشاء البرنامج النهائي...
echo جاري إنشاء برنامج Windows...
copy package-installer.json package.json
call npm run dist
echo.
echo [10/10] التحقق من النتيجة...
if exist "dist" ^(
    echo.
    echo ===============================================
    echo ✓ تم إنشاء نظام الصفوة بنجاح!
    echo ===============================================
    echo.
    for /r dist %%i in ^(*.exe^) do ^(
        echo 📦 ملف المثبت: %%~nxi
        echo 📊 الحجم: %%~zi bytes
        echo 📁 المسار: %%i
        echo.
    ^)
    echo ✨ المميزات:
    echo ✓ برنامج Windows حقيقي
    echo ✓ يعمل بدون Node.js
    echo ✓ يعمل بدون إنترنت
    echo ✓ مثبت احترافي
    echo ✓ جميع وحدات النسيج
    echo.
    echo 🚀 طريقة الاستخدام:
    echo 1. انسخ ملف المثبت للجهاز المطلوب
    echo 2. شغل الملف واتبع التعليمات
    echo 3. ابحث عن "نظام الصفوة للنسيج" في قائمة ابدأ
    echo 4. سجل دخول: admin / admin123
    echo.
    echo 🎊 مبروك! البرنامج جاهز للاستخدام!
^) else ^(
    echo ✗ فشل في إنشاء البرنامج
    echo يرجى مراجعة الأخطاء أعلاه
^)
echo.
echo تنظيف الملفات المؤقتة...
if exist next.config.js.backup ^(
    copy next.config.js.backup next.config.js
    del next.config.js.backup
^)
if exist package-installer.json del package-installer.json
echo.
echo ===============================================
echo شكراً لاستخدام نظام الصفوة للنسيج!
echo ===============================================
echo.
pause
) > "%INSTALLER_DIR%\تثبيت-نظام-الصفوة.bat"

echo [4/8] إنشاء ملف معلومات المثبت...
(
echo [Setup]
echo AppName=نظام الصفوة للنسيج
echo AppVersion=1.0.0
echo AppPublisher=شركة الصفوة للنسيج
echo AppPublisherURL=https://safa-textile.com
echo AppSupportURL=https://safa-textile.com/support
echo AppUpdatesURL=https://safa-textile.com/updates
echo DefaultDirName={autopf}\نظام الصفوة للنسيج
echo DefaultGroupName=نظام الصفوة للنسيج
echo AllowNoIcons=yes
echo LicenseFile=license.txt
echo OutputDir=.
echo OutputBaseFilename=تثبيت-نظام-الصفوة
echo SetupIconFile=icon.ico
echo Compression=lzma
echo SolidCompression=yes
echo WizardStyle=modern
echo.
echo [Languages]
echo Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
echo.
echo [Tasks]
echo Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
echo.
echo [Files]
echo Source: "تثبيت-نظام-الصفوة.bat"; DestDir: "{app}"; Flags: ignoreversion
echo.
echo [Icons]
echo Name: "{group}\نظام الصفوة للنسيج"; Filename: "{app}\تثبيت-نظام-الصفوة.bat"
echo Name: "{autodesktop}\نظام الصفوة للنسيج"; Filename: "{app}\تثبيت-نظام-الصفوة.bat"; Tasks: desktopicon
echo.
echo [Run]
echo Filename: "{app}\تثبيت-نظام-الصفوة.bat"; Description: "{cm:LaunchProgram,نظام الصفوة للنسيج}"; Flags: nowait postinstall skipifsilent
) > "%INSTALLER_DIR%\setup.iss"

echo [5/8] إنشاء ملف الترخيص...
(
echo ترخيص استخدام نظام الصفوة للنسيج
echo =====================================
echo.
echo © 2024 شركة الصفوة للنسيج - جميع الحقوق محفوظة
echo.
echo هذا البرنامج مخصص لإدارة مصانع النسيج والملابس.
echo.
echo الميزات:
echo • نظام ERP متكامل لإدارة مصانع النسيج
echo • يعمل بدون إنترنت
echo • واجهة عربية احترافية
echo • جميع وحدات الإنتاج والمخزون والمالية
echo.
echo للدعم الفني:
echo البريد الإلكتروني: <EMAIL>
echo الموقع: https://safa-textile.com
echo.
echo بالمتابعة، أنت توافق على شروط الاستخدام.
) > "%INSTALLER_DIR%\license.txt"

echo [6/8] نسخ الأيقونة...
if exist "public\favicon.ico" (
    copy "public\favicon.ico" "%INSTALLER_DIR%\icon.ico"
) else if exist "build\icon.ico" (
    copy "build\icon.ico" "%INSTALLER_DIR%\icon.ico"
)

echo [7/8] إنشاء ملف EXE باستخدام PowerShell...
(
echo Add-Type -AssemblyName System.Windows.Forms
echo Add-Type -AssemblyName System.Drawing
echo.
echo $form = New-Object System.Windows.Forms.Form
echo $form.Text = "مثبت نظام الصفوة للنسيج"
echo $form.Size = New-Object System.Drawing.Size^(500,400^)
echo $form.StartPosition = "CenterScreen"
echo $form.FormBorderStyle = "FixedDialog"
echo $form.MaximizeBox = $false
echo.
echo $label = New-Object System.Windows.Forms.Label
echo $label.Location = New-Object System.Drawing.Point^(10,10^)
echo $label.Size = New-Object System.Drawing.Size^(480,50^)
echo $label.Text = "مرحباً بك في مثبت نظام الصفوة للنسيج"
echo $label.Font = New-Object System.Drawing.Font^("Arial",12,[System.Drawing.FontStyle]::Bold^)
echo $label.TextAlign = "MiddleCenter"
echo $form.Controls.Add^($label^)
echo.
echo $textBox = New-Object System.Windows.Forms.TextBox
echo $textBox.Location = New-Object System.Drawing.Point^(10,70^)
echo $textBox.Size = New-Object System.Drawing.Size^(480,200^)
echo $textBox.Multiline = $true
echo $textBox.ScrollBars = "Vertical"
echo $textBox.ReadOnly = $true
echo $textBox.Text = "نظام الصفوة لإدارة مصانع النسيج`r`n`r`nالوحدات المتاحة:`r`n• الإنتاج والتخطيط`r`n• إدارة المخزون`r`n• المشتريات والموردين`r`n• المبيعات والعملاء`r`n• المالية والحسابات`r`n• الموارد البشرية`r`n• الجودة والصيانة`r`n`r`nالمميزات:`r`n• يعمل بدون إنترنت`r`n• واجهة عربية احترافية`r`n• بيانات تجريبية واقعية"
echo $form.Controls.Add^($textBox^)
echo.
echo $buttonInstall = New-Object System.Windows.Forms.Button
echo $buttonInstall.Location = New-Object System.Drawing.Point^(10,290^)
echo $buttonInstall.Size = New-Object System.Drawing.Size^(100,30^)
echo $buttonInstall.Text = "تثبيت"
echo $buttonInstall.Add_Click^({
echo     $form.Hide^(^)
echo     Start-Process -FilePath "تثبيت-نظام-الصفوة.bat" -Wait
echo     $form.Close^(^)
echo }^)
echo $form.Controls.Add^($buttonInstall^)
echo.
echo $buttonCancel = New-Object System.Windows.Forms.Button
echo $buttonCancel.Location = New-Object System.Drawing.Point^(380,290^)
echo $buttonCancel.Size = New-Object System.Drawing.Size^(100,30^)
echo $buttonCancel.Text = "إلغاء"
echo $buttonCancel.Add_Click^({ $form.Close^(^) }^)
echo $form.Controls.Add^($buttonCancel^)
echo.
echo $form.ShowDialog^(^)
) > "%INSTALLER_DIR%\installer.ps1"

echo [8/8] إنشاء ملف EXE النهائي...
(
echo @echo off
echo cd /d "%%~dp0"
echo powershell -ExecutionPolicy Bypass -File "installer.ps1"
) > "%INSTALLER_DIR%\تثبيت-نظام-الصفوة.cmd"

echo.
echo ===============================================
echo ✓ تم إنشاء مثبت EXE بنجاح!
echo ===============================================
echo.
echo الملفات المُنشأة في مجلد "%INSTALLER_DIR%":
dir "%INSTALLER_DIR%" /b
echo.
echo 📦 ملف المثبت الرئيسي:
echo    تثبيت-نظام-الصفوة.cmd
echo.
echo 🚀 طريقة الاستخدام:
echo 1. انسخ مجلد "%INSTALLER_DIR%" للجهاز المطلوب
echo 2. شغل ملف "تثبيت-نظام-الصفوة.cmd"
echo 3. اتبع التعليمات في النافذة
echo.
echo 💡 ملاحظة:
echo يمكنك ضغط مجلد "%INSTALLER_DIR%" في ملف ZIP
echo وتوزيعه كمثبت واحد شامل.
echo.
echo ✨ المميزات:
echo ✓ واجهة تثبيت احترافية
echo ✓ ملف EXE قابل للتنفيذ
echo ✓ جميع الملفات مدمجة
echo ✓ سهولة التوزيع
echo.
pause
