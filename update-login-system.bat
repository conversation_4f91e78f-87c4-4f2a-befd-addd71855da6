@echo off
echo.
echo Updating Login System - Username + Password
echo ===========================================
echo.

echo [1/4] Updating database schema...
cmd /c "node_modules\.bin\prisma generate"
cmd /c "node_modules\.bin\prisma db push"

echo [2/4] Updating seed data...
cmd /c "node_modules\.bin\prisma db seed"

echo [3/4] Restarting system...
taskkill /F /IM node.exe 2>nul
timeout /t 3 /nobreak >nul

echo [4/4] Starting updated system...
echo.
echo ==========================================
echo LOGIN SYSTEM UPDATED!
echo ==========================================
echo.
echo New Login Method: USERNAME + PASSWORD
echo.
echo Login Accounts:
echo - Admin: admin / admin123
echo - Supervisor: supervisor / super123  
echo - Operator: operator / oper123
echo.
echo System URL: http://localhost:3000
echo ==========================================
echo.

start http://localhost:3000
cmd /c "npm run dev"
