'use client'

import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { MoreHorizontal, Edit, Trash2, Eye, AlertTriangle } from 'lucide-react'

// بيانات تجريبية - سيتم استبدالها بـ API
const mockMaterials = [
  {
    id: '1',
    code: 'MAT001',
    nameAr: 'قطن خام عالي الجودة',
    nameEn: 'High Quality Raw Cotton',
    type: 'RAW_MATERIAL',
    unit: 'كيلو',
    currentStock: 2500,
    minStock: 1000,
    maxStock: 5000,
    unitCost: 15.50,
    supplier: 'شركة القطن المصري',
    isActive: true,
  },
  {
    id: '2',
    code: 'MAT002',
    nameAr: 'صبغة زرقاء',
    nameEn: 'Blue Dye',
    type: 'RAW_MATERIAL',
    unit: 'لتر',
    currentStock: 250,
    minStock: 100,
    maxStock: 500,
    unitCost: 45.00,
    supplier: 'مصنع الألوان الحديث',
    isActive: true,
  },
  {
    id: '3',
    code: 'MAT003',
    nameAr: 'صبغة حمراء',
    nameEn: 'Red Dye',
    type: 'RAW_MATERIAL',
    unit: 'لتر',
    currentStock: 80,
    minStock: 100,
    maxStock: 500,
    unitCost: 42.00,
    supplier: 'مصنع الألوان الحديث',
    isActive: true,
  },
  {
    id: '4',
    code: 'MAT004',
    nameAr: 'خيوط بوليستر',
    nameEn: 'Polyester Thread',
    type: 'RAW_MATERIAL',
    unit: 'كيلو',
    currentStock: 150,
    minStock: 50,
    maxStock: 300,
    unitCost: 25.00,
    supplier: 'شركة الخيوط المتقدمة',
    isActive: true,
  },
]

const materialTypeLabels = {
  RAW_MATERIAL: 'مواد خام',
  SEMI_FINISHED: 'نصف مصنعة',
  CONSUMABLE: 'مواد استهلاكية',
  SPARE_PARTS: 'قطع غيار',
}

export function MaterialsList() {
  const [materials, setMaterials] = useState(mockMaterials)

  const getStockStatus = (current: number, min: number) => {
    if (current <= min * 0.5) {
      return { status: 'critical', label: 'حرج', color: 'bg-red-100 text-red-800' }
    } else if (current <= min) {
      return { status: 'low', label: 'منخفض', color: 'bg-yellow-100 text-yellow-800' }
    } else {
      return { status: 'normal', label: 'طبيعي', color: 'bg-green-100 text-green-800' }
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'RAW_MATERIAL':
        return 'bg-blue-100 text-blue-800'
      case 'SEMI_FINISHED':
        return 'bg-purple-100 text-purple-800'
      case 'CONSUMABLE':
        return 'bg-orange-100 text-orange-800'
      case 'SPARE_PARTS':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الكود</TableHead>
            <TableHead>اسم المادة</TableHead>
            <TableHead>النوع</TableHead>
            <TableHead>المخزون الحالي</TableHead>
            <TableHead>حالة المخزون</TableHead>
            <TableHead>تكلفة الوحدة</TableHead>
            <TableHead>المورد</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {materials.map((material) => {
            const stockStatus = getStockStatus(material.currentStock, material.minStock)
            
            return (
              <TableRow key={material.id}>
                <TableCell className="font-medium">
                  {material.code}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{material.nameAr}</div>
                    {material.nameEn && (
                      <div className="text-sm text-muted-foreground">
                        {material.nameEn}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getTypeColor(material.type)}>
                    {materialTypeLabels[material.type as keyof typeof materialTypeLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {formatNumber(material.currentStock)} {material.unit}
                    {stockStatus.status === 'critical' && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    الحد الأدنى: {formatNumber(material.minStock)} {material.unit}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={stockStatus.color}>
                    {stockStatus.label}
                  </Badge>
                </TableCell>
                <TableCell>
                  {formatCurrency(material.unitCost)}
                </TableCell>
                <TableCell>
                  <div className="text-sm">{material.supplier}</div>
                </TableCell>
                <TableCell>
                  <Badge variant={material.isActive ? 'default' : 'secondary'}>
                    {material.isActive ? 'نشط' : 'غير نشط'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="ml-2 h-4 w-4" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="ml-2 h-4 w-4" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="ml-2 h-4 w-4" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
