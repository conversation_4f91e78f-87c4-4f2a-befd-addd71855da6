'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  ArrowUp,
  ArrowDown,
  RefreshCw,
  Package,
  TrendingUp,
  TrendingDown,
  Plus,
  Filter,
  Search,
  Download
} from 'lucide-react'

interface InventoryMovement {
  id: string
  itemName: string
  itemCode: string
  movementType: string
  quantity: number
  unit: string
  unitPrice: number
  totalValue: number
  reference: string
  notes?: string
  createdBy: string
  createdAt: Date
  location: string
}

const mockMovements: InventoryMovement[] = [
  {
    id: '1',
    itemName: 'قطن خام عالي الجودة',
    itemCode: 'MAT001',
    movementType: 'IN',
    quantity: 1000,
    unit: 'كيلو',
    unitPrice: 15.50,
    totalValue: 15500,
    reference: 'PO-2024-001',
    notes: 'استلام من المورد الرئيسي',
    createdBy: 'أحمد محمد',
    createdAt: new Date('2024-01-15T10:30:00'),
    location: 'مخزن المواد الخام'
  },
  {
    id: '2',
    itemName: 'صبغة زرقاء',
    itemCode: 'MAT002',
    movementType: 'OUT',
    quantity: -10,
    unit: 'لتر',
    unitPrice: 45.00,
    totalValue: -450,
    reference: 'WO-2024-001',
    notes: 'استخدام في أمر العمل',
    createdBy: 'فاطمة علي',
    createdAt: new Date('2024-01-15T14:15:00'),
    location: 'ورشة الصباغة'
  },
  {
    id: '3',
    itemName: 'قميص قطني أزرق',
    itemCode: 'PRD001',
    movementType: 'PRODUCTION',
    quantity: 50,
    unit: 'قطعة',
    unitPrice: 75.00,
    totalValue: 3750,
    reference: 'WO-2024-001',
    notes: 'إنتاج مرحلة أولى',
    createdBy: 'محمد أحمد',
    createdAt: new Date('2024-01-16T09:00:00'),
    location: 'مخزن المنتجات النهائية'
  },
  {
    id: '4',
    itemName: 'قطن خام عالي الجودة',
    itemCode: 'MAT001',
    movementType: 'ADJUSTMENT',
    quantity: -25,
    unit: 'كيلو',
    unitPrice: 15.50,
    totalValue: -387.50,
    reference: 'ADJ-001',
    notes: 'تسوية جرد - نقص في المخزون',
    createdBy: 'سارة محمود',
    createdAt: new Date('2024-01-16T16:45:00'),
    location: 'مخزن المواد الخام'
  },
  {
    id: '5',
    itemName: 'قميص قطني أزرق',
    itemCode: 'PRD001',
    movementType: 'SALE',
    quantity: -30,
    unit: 'قطعة',
    unitPrice: 120.00,
    totalValue: -3600,
    reference: 'SO-2024-001',
    notes: 'بيع للعميل الرئيسي',
    createdBy: 'خالد عبدالله',
    createdAt: new Date('2024-01-17T11:20:00'),
    location: 'مخزن المنتجات النهائية'
  }
]

const getMovementTypeColor = (type: string) => {
  switch (type) {
    case 'IN': return 'bg-green-500'
    case 'OUT': return 'bg-red-500'
    case 'PRODUCTION': return 'bg-blue-500'
    case 'SALE': return 'bg-purple-500'
    case 'PURCHASE': return 'bg-green-600'
    case 'ADJUSTMENT': return 'bg-yellow-500'
    case 'TRANSFER': return 'bg-orange-500'
    case 'RETURN': return 'bg-gray-500'
    default: return 'bg-gray-500'
  }
}

const getMovementTypeText = (type: string) => {
  switch (type) {
    case 'IN': return 'دخول'
    case 'OUT': return 'خروج'
    case 'PRODUCTION': return 'إنتاج'
    case 'SALE': return 'بيع'
    case 'PURCHASE': return 'شراء'
    case 'ADJUSTMENT': return 'تسوية'
    case 'TRANSFER': return 'نقل'
    case 'RETURN': return 'إرجاع'
    default: return 'غير محدد'
  }
}

const getMovementIcon = (type: string) => {
  switch (type) {
    case 'IN':
    case 'PRODUCTION':
    case 'PURCHASE':
    case 'RETURN':
      return <ArrowUp className="h-4 w-4" />
    case 'OUT':
    case 'SALE':
      return <ArrowDown className="h-4 w-4" />
    case 'TRANSFER':
      return <RefreshCw className="h-4 w-4" />
    case 'ADJUSTMENT':
      return <Package className="h-4 w-4" />
    default:
      return <Package className="h-4 w-4" />
  }
}

export default function InventoryMovementsPage() {
  const [movements, setMovements] = useState<InventoryMovement[]>(mockMovements)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')

  const filteredMovements = movements.filter(movement => {
    const matchesSearch = movement.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         movement.itemCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         movement.reference.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || movement.movementType === selectedType
    return matchesSearch && matchesType
  })

  const totalInValue = movements
    .filter(m => m.quantity > 0)
    .reduce((acc, m) => acc + m.totalValue, 0)

  const totalOutValue = movements
    .filter(m => m.quantity < 0)
    .reduce((acc, m) => acc + Math.abs(m.totalValue), 0)

  const netValue = totalInValue - totalOutValue

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">حركة المخزون</h1>
          <p className="text-gray-600">متابعة جميع حركات الدخول والخروج للمخزون</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            حركة جديدة
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الحركات</p>
                <p className="text-2xl font-bold text-blue-600">{movements.length}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيمة الداخل</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalInValue.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيمة الخارج</p>
                <p className="text-2xl font-bold text-red-600">
                  {totalOutValue.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">صافي القيمة</p>
                <p className={`text-2xl font-bold ${netValue >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netValue.toLocaleString()} ر.س
                </p>
              </div>
              <RefreshCw className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الحركات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="IN">دخول</option>
                <option value="OUT">خروج</option>
                <option value="PRODUCTION">إنتاج</option>
                <option value="SALE">بيع</option>
                <option value="PURCHASE">شراء</option>
                <option value="ADJUSTMENT">تسوية</option>
                <option value="TRANSFER">نقل</option>
                <option value="RETURN">إرجاع</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Movements Table */}
      <Card>
        <CardHeader>
          <CardTitle>سجل حركات المخزون</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredMovements.map((movement) => (
              <div key={movement.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getMovementTypeColor(movement.movementType)}>
                      {getMovementIcon(movement.movementType)}
                      <span className="mr-1">{getMovementTypeText(movement.movementType)}</span>
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{movement.itemName}</h3>
                      <p className="text-sm text-gray-600">كود: {movement.itemCode}</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className={`text-lg font-bold ${movement.quantity >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {movement.quantity >= 0 ? '+' : ''}{movement.quantity} {movement.unit}
                    </p>
                    <p className="text-sm text-gray-600">
                      {movement.totalValue.toLocaleString()} ر.س
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">المرجع</p>
                    <p className="font-medium">{movement.reference}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الموقع</p>
                    <p className="font-medium">{movement.location}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المستخدم</p>
                    <p className="font-medium">{movement.createdBy}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">التاريخ</p>
                    <p className="font-medium">
                      {movement.createdAt.toLocaleDateString('ar-SA')} {movement.createdAt.toLocaleTimeString('ar-SA')}
                    </p>
                  </div>
                </div>

                {movement.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {movement.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredMovements.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد حركات مخزون تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
