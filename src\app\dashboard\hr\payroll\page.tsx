'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  DollarSign,
  Calculator,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Plus,
  CheckCircle
} from 'lucide-react'

interface PayrollRecord {
  id: string
  employeeId: string
  employeeName: string
  department: string
  position: string
  payPeriod: string
  payType: string
  baseSalary: number
  workingDays: number
  workingHours: number
  overtimeHours: number
  overtimeRate: number
  allowances: number
  deductions: number
  grossSalary: number
  netSalary: number
  status: string
  payDate?: Date
  notes?: string
}

const mockPayroll: PayrollRecord[] = [
  {
    id: '1',
    employeeId: 'EMP001',
    employeeName: 'أحمد محمد',
    department: 'الإنتاج',
    position: 'مشغل ماكينة',
    payPeriod: '2024-01',
    payType: 'DAILY',
    baseSalary: 150,
    workingDays: 26,
    workingHours: 208,
    overtimeHours: 15,
    overtimeRate: 1.5,
    allowances: 500,
    deductions: 200,
    grossSalary: 4675,
    netSalary: 4475,
    status: 'PAID',
    payDate: new Date('2024-01-31'),
    notes: 'راتب شهر يناير - مدفوع'
  },
  {
    id: '2',
    employeeId: 'EMP002',
    employeeName: 'فاطمة علي',
    department: 'الجودة',
    position: 'مفتش جودة',
    payPeriod: '2024-01',
    payType: 'MONTHLY',
    baseSalary: 4000,
    workingDays: 26,
    workingHours: 208,
    overtimeHours: 8,
    overtimeRate: 1.5,
    allowances: 300,
    deductions: 150,
    grossSalary: 4492,
    netSalary: 4342,
    status: 'PENDING',
    notes: 'في انتظار الاعتماد'
  },
  {
    id: '3',
    employeeId: 'EMP003',
    employeeName: 'محمد أحمد',
    department: 'الصيانة',
    position: 'فني صيانة',
    payPeriod: '2024-01',
    payType: 'DAILY',
    baseSalary: 180,
    workingDays: 26,
    workingHours: 208,
    overtimeHours: 25,
    overtimeRate: 1.5,
    allowances: 600,
    deductions: 100,
    grossSalary: 5855,
    netSalary: 5755,
    status: 'APPROVED',
    notes: 'معتمد - جاهز للدفع'
  },
  {
    id: '4',
    employeeId: 'EMP004',
    employeeName: 'سارة محمود',
    department: 'المبيعات',
    position: 'مندوب مبيعات',
    payPeriod: '2024-01',
    payType: 'MONTHLY',
    baseSalary: 3500,
    workingDays: 24,
    workingHours: 192,
    overtimeHours: 0,
    overtimeRate: 1.5,
    allowances: 800,
    deductions: 250,
    grossSalary: 4050,
    netSalary: 3800,
    status: 'DRAFT',
    notes: 'مسودة - يحتاج مراجعة'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'bg-gray-500'
    case 'PENDING': return 'bg-yellow-500'
    case 'APPROVED': return 'bg-blue-500'
    case 'PAID': return 'bg-green-500'
    case 'REJECTED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'مسودة'
    case 'PENDING': return 'في الانتظار'
    case 'APPROVED': return 'معتمد'
    case 'PAID': return 'مدفوع'
    case 'REJECTED': return 'مرفوض'
    default: return 'غير محدد'
  }
}

const getPayTypeText = (type: string) => {
  switch (type) {
    case 'DAILY': return 'يومي'
    case 'MONTHLY': return 'شهري'
    case 'HOURLY': return 'بالساعة'
    default: return 'غير محدد'
  }
}

const getPayTypeColor = (type: string) => {
  switch (type) {
    case 'DAILY': return 'bg-blue-500'
    case 'MONTHLY': return 'bg-green-500'
    case 'HOURLY': return 'bg-purple-500'
    default: return 'bg-gray-500'
  }
}

export default function PayrollPage() {
  const [payroll, setPayroll] = useState<PayrollRecord[]>(mockPayroll)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all')
  const [selectedPayType, setSelectedPayType] = useState<string>('all')

  const filteredPayroll = payroll.filter(record => {
    const matchesSearch = record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || record.status === selectedStatus
    const matchesDepartment = selectedDepartment === 'all' || record.department === selectedDepartment
    const matchesPayType = selectedPayType === 'all' || record.payType === selectedPayType
    return matchesSearch && matchesStatus && matchesDepartment && matchesPayType
  })

  const departments = Array.from(new Set(payroll.map(record => record.department)))

  const totalGrossSalary = payroll.reduce((acc, r) => acc + r.grossSalary, 0)
  const totalNetSalary = payroll.reduce((acc, r) => acc + r.netSalary, 0)
  const totalDeductions = payroll.reduce((acc, r) => acc + r.deductions, 0)
  const totalAllowances = payroll.reduce((acc, r) => acc + r.allowances, 0)

  const handleApprove = (recordId: string) => {
    setPayroll(payroll.map(record => 
      record.id === recordId ? { ...record, status: 'APPROVED' } : record
    ))
  }

  const handlePay = (recordId: string) => {
    setPayroll(payroll.map(record => 
      record.id === recordId 
        ? { 
            ...record, 
            status: 'PAID',
            payDate: new Date()
          }
        : record
    ))
  }

  const calculateDailySalary = (record: PayrollRecord) => {
    if (record.payType === 'DAILY') {
      const dailyRate = record.baseSalary
      const regularPay = dailyRate * record.workingDays
      const overtimePay = (dailyRate / 8) * record.overtimeHours * record.overtimeRate
      return regularPay + overtimePay + record.allowances - record.deductions
    }
    return record.netSalary
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الرواتب</h1>
          <p className="text-gray-600">إدارة رواتب الموظفين (يومي/شهري)</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير كشف المرتبات
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            حساب راتب جديد
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الرواتب الإجمالية</p>
                <p className="text-2xl font-bold text-blue-600">
                  {totalGrossSalary.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الرواتب الصافية</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalNetSalary.toLocaleString()} ر.س
                </p>
              </div>
              <Calculator className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي البدلات</p>
                <p className="text-2xl font-bold text-purple-600">
                  {totalAllowances.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الخصومات</p>
                <p className="text-2xl font-bold text-red-600">
                  {totalDeductions.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث عن موظف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="DRAFT">مسودة</option>
                <option value="PENDING">في الانتظار</option>
                <option value="APPROVED">معتمد</option>
                <option value="PAID">مدفوع</option>
                <option value="REJECTED">مرفوض</option>
              </select>
              
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>

              <select
                value={selectedPayType}
                onChange={(e) => setSelectedPayType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="DAILY">يومي</option>
                <option value="MONTHLY">شهري</option>
                <option value="HOURLY">بالساعة</option>
              </select>

              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payroll Records */}
      <Card>
        <CardHeader>
          <CardTitle>كشف المرتبات - يناير 2024</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredPayroll.map((record) => (
              <div key={record.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(record.status)}>
                      {getStatusText(record.status)}
                    </Badge>
                    <Badge className={getPayTypeColor(record.payType)}>
                      {getPayTypeText(record.payType)}
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{record.employeeName}</h3>
                      <p className="text-sm text-gray-600">
                        {record.employeeId} - {record.position}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {record.status === 'PENDING' && (
                      <Button 
                        size="sm"
                        onClick={() => handleApprove(record.id)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <CheckCircle className="h-4 w-4 ml-1" />
                        اعتماد
                      </Button>
                    )}
                    {record.status === 'APPROVED' && (
                      <Button 
                        size="sm"
                        onClick={() => handlePay(record.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <DollarSign className="h-4 w-4 ml-1" />
                        دفع
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">القسم</p>
                    <p className="font-medium">{record.department}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الراتب الأساسي</p>
                    <p className="font-medium">
                      {record.baseSalary.toLocaleString()} ر.س
                      {record.payType === 'DAILY' && ' / يوم'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">أيام العمل</p>
                    <p className="font-medium">{record.workingDays} يوم</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">ساعات إضافية</p>
                    <p className="font-medium text-blue-600">{record.overtimeHours} ساعة</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">البدلات</p>
                    <p className="font-medium text-green-600">+{record.allowances.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الخصومات</p>
                    <p className="font-medium text-red-600">-{record.deductions.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الراتب الإجمالي</p>
                    <p className="font-medium">{record.grossSalary.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الراتب الصافي</p>
                    <p className="font-bold text-green-600">{record.netSalary.toLocaleString()} ر.س</p>
                  </div>
                </div>

                {record.payType === 'DAILY' && (
                  <div className="mb-3 p-2 bg-blue-100 rounded text-sm">
                    <strong>حساب الراتب اليومي:</strong> 
                    {record.baseSalary} ر.س × {record.workingDays} يوم = {(record.baseSalary * record.workingDays).toLocaleString()} ر.س
                    {record.overtimeHours > 0 && (
                      <span> + ساعات إضافية: {((record.baseSalary / 8) * record.overtimeHours * record.overtimeRate).toLocaleString()} ر.س</span>
                    )}
                  </div>
                )}

                {record.payDate && (
                  <div className="mb-3 p-2 bg-green-100 rounded text-sm">
                    <strong>تاريخ الدفع:</strong> {record.payDate.toLocaleDateString('ar-SA')}
                  </div>
                )}

                {record.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {record.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredPayroll.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد سجلات رواتب تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
