# نظام إدارة موارد المؤسسة للنسيج (Textile ERP System)

## نظرة عامة
نظام ERP شامل مصمم خصيصاً لمصانع النسيج يشمل جميع العمليات من المواد الخام إلى المنتج النهائي.

## الوحدات الأساسية

### 1. وحدة الإنتاج (Production Module)
- تخطيط الإنتاج (MRP)
- أوامر التشغيل
- تتبع مراحل الإنتاج: الغزل → النسيج → الصباغة → التشطيب
- إدارة خطوط الإنتاج والماكينات
- مراقبة الأداء والإنتاجية
- حساب تكلفة المنتج النهائي

### 2. وحدة المخزون (Inventory Management)
- إدارة المواد الخام (قطن، ألياف، ألوان...)
- إدارة المخزون نصف المصنع والمصنع
- تحديد الحد الأدنى والحد الأعلى للمخزون
- تتبع الدفعات (Batch Tracking)
- الجرد الدوري والأوتوماتيكي

### 3. وحدة المشتريات (Purchasing Module)
- إدارة الموردين
- طلبات الشراء والعروض
- أوامر الشراء والتسليم
- تتبع الفواتير

### 4. وحدة المبيعات والتوزيع (Sales & Distribution)
- عروض الأسعار
- أوامر البيع
- إدارة العملاء
- تتبع الشحن والفوترة

### 5. وحدة الجودة (Quality Control)
- الفحص أثناء الاستلام
- فحص أثناء الإنتاج وبعده
- إدارة تقارير الجودة والرفض
- تحديد معايير الجودة

### 6. وحدة المالية والمحاسبة (Finance & Accounting)
- القيود اليومية والحسابات العامة
- الذمم المدينة والدائنة
- حساب تكلفة الإنتاج
- إعداد الميزانيات والتقارير المالية

### 7. وحدة الموارد البشرية (HRM)
- إدارة الموظفين والفنيين
- الحضور والانصراف
- الرواتب والحوافز
- تقييم الأداء

### 8. وحدة الصيانة (Maintenance)
- إدارة صيانة الماكينات
- سجل أعطال المعدات
- تتبع قطع الغيار

## المميزات الإضافية
- لوحة تحكم تفاعلية لكل قسم
- نظام تنبيهات وإشعارات تلقائية
- تقارير فورية وتاريخية
- إمكانية الربط بالأجهزة (IoT)
- تطبيق موبايل للمشرفين
- دعم اللغة العربية (RTL)

## التقنيات المستخدمة
- **Frontend**: Next.js 14 مع TypeScript
- **Backend**: Node.js مع Express
- **Database**: PostgreSQL مع Prisma ORM
- **Authentication**: NextAuth.js
- **UI**: Tailwind CSS مع shadcn/ui
- **Language**: دعم العربية والإنجليزية

## متطلبات التثبيت

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
https://nodejs.org/

### 2. تثبيت PostgreSQL
قم بتحميل وتثبيت PostgreSQL من:
https://www.postgresql.org/download/

### 3. تشغيل المشروع
```bash
# تثبيت المكتبات
npm install

# إعداد قاعدة البيانات
npx prisma migrate dev

# تشغيل الخادم
npm run dev
```

## هيكل المشروع
```
textile-erp/
├── src/
│   ├── app/                 # صفحات Next.js
│   ├── components/          # مكونات واجهة المستخدم
│   ├── lib/                 # مكتبات مساعدة
│   ├── types/               # تعريفات TypeScript
│   └── utils/               # وظائف مساعدة
├── prisma/                  # مخططات قاعدة البيانات
├── public/                  # الملفات العامة
└── docs/                    # الوثائق
```

## الحالة الحالية
🚀 **المرحلة الثانية: تطوير الوحدات الأساسية**

### ✅ مكتمل
- **البنية الأساسية**: Next.js 14 + TypeScript + Tailwind CSS
- **قاعدة البيانات**: PostgreSQL مع Prisma ORM
- **المصادقة**: NextAuth.js مع تشفير كلمات المرور
- **واجهة المستخدم**: مكونات عربية (RTL) مع shadcn/ui
- **لوحة التحكم**: إحصائيات تفاعلية ومخططات
- **وحدة المخزون**:
  - ✅ إدارة المواد الخام (عرض، إضافة، تعديل، حذف)
  - ✅ إدارة المنتجات (عرض، إضافة، تعديل، حذف)
  - ✅ تنبيهات المخزون المنخفض والحرج
  - ✅ تصفية وبحث متقدم
- **وحدة الإنتاج**:
  - ✅ إدارة أوامر العمل (عرض، إضافة، تعديل)
  - ✅ إحصائيات أوامر العمل
  - ✅ تتبع التقدم والحالة
  - ✅ تحديد الأولويات

### 🚧 قيد التطوير
- **وحدة الإنتاج**: مراحل الإنتاج وتخطيط الإنتاج
- **وحدة المشتريات**: الموردين وأوامر الشراء
- **وحدة المبيعات**: العملاء وأوامر البيع
- **وحدة الجودة**: فحوصات الجودة والتقارير
- **وحدة المالية**: الحسابات والفواتير
- **وحدة الموارد البشرية**: الموظفين والرواتب
- **وحدة الصيانة**: صيانة المعدات

### 📋 المخطط للمرحلة التالية
- API endpoints للعمليات CRUD
- تقارير مفصلة وتحليلات
- نظام الإشعارات المتقدم
- تطبيق موبايل
- ربط IoT للماكينات

## المساهمة
هذا المشروع مفتوح للتطوير والتحسين.

## الترخيص
MIT License
