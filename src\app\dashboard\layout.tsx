import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { Sidebar } from '@/components/layout/sidebar'
import { Header } from '@/components/layout/header'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/')
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* الشريط الجانبي */}
        <Sidebar />
        
        {/* المحتوى الرئيسي */}
        <div className="flex-1 flex flex-col">
          {/* الرأس */}
          <Header user={session.user} />
          
          {/* المحتوى */}
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
