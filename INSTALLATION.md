# دليل التثبيت والتشغيل - نظام إدارة النسيج

## المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل Node.js من الموقع الرسمي: https://nodejs.org/
- اختر النسخة LTS (الموصى بها)
- تأكد من تثبيت npm معه

للتحقق من التثبيت:
```bash
node --version
npm --version
```

### 2. تثبيت PostgreSQL
- قم بتحميل PostgreSQL من: https://www.postgresql.org/download/
- اتبع تعليمات التثبيت لنظام التشغيل الخاص بك
- احفظ كلمة مرور المستخدم postgres

### 3. إنشا<PERSON> قاعدة البيانات
افتح PostgreSQL command line أو pgAdmin وقم بتنفيذ:
```sql
CREATE DATABASE textile_erp;
```

## خطوات التثبيت

### 1. تثبيت المكتبات
```bash
npm install
```

### 2. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env` وقم بتعديل القيم:
```bash
cp .env.example .env
```

قم بتعديل ملف `.env`:
```env
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/textile_erp"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء الجداول
npx prisma migrate dev

# إضافة البيانات التجريبية
npm run db:seed
```

### 4. تشغيل النظام
```bash
npm run dev
```

سيعمل النظام على: http://localhost:3000

## الحسابات التجريبية

بعد تشغيل البيانات التجريبية، يمكنك استخدام هذه الحسابات:

### مدير النظام
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### مشرف الإنتاج
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `super123`

### مشغل الماكينة
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `oper123`

## أوامر مفيدة

### إدارة قاعدة البيانات
```bash
# عرض قاعدة البيانات في المتصفح
npm run db:studio

# إعادة تعيين قاعدة البيانات
npx prisma migrate reset

# تحديث مخطط قاعدة البيانات
npx prisma db push

# إنشاء migration جديد
npx prisma migrate dev --name migration_name
```

### التطوير
```bash
# تشغيل النظام في وضع التطوير
npm run dev

# بناء النظام للإنتاج
npm run build

# تشغيل النظام في وضع الإنتاج
npm run start

# فحص الكود
npm run lint
```

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل PostgreSQL
2. تحقق من صحة `DATABASE_URL` في ملف `.env`
3. تأكد من وجود قاعدة البيانات `textile_erp`

### خطأ في تثبيت المكتبات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### خطأ في Prisma
```bash
# إعادة إنشاء Prisma Client
npx prisma generate
```

## الميزات المتاحة حالياً

### ✅ مكتمل
- نظام المصادقة والتسجيل
- لوحة التحكم الرئيسية
- الشريط الجانبي والتنقل
- إعداد قاعدة البيانات
- البيانات التجريبية
- دعم اللغة العربية (RTL)

### 🚧 قيد التطوير
- وحدة الإنتاج
- وحدة المخزون
- وحدة المشتريات
- وحدة المبيعات
- وحدة الجودة
- وحدة المالية
- وحدة الموارد البشرية
- وحدة الصيانة

## المساهمة في التطوير

1. قم بعمل Fork للمشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود واختبره
4. قم بعمل commit للتغييرات
5. ادفع التغييرات إلى branch
6. أنشئ Pull Request

## الدعم

للحصول على المساعدة:
1. راجع الوثائق في مجلد `docs/`
2. تحقق من Issues في GitHub
3. تواصل مع فريق التطوير

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
