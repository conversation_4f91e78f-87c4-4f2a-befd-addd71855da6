import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { LoginForm } from '@/components/auth/login-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Logo } from '@/components/ui/logo'

export default async function HomePage() {
  const session = await getServerSession(authOptions)

  if (session) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50 flex items-center justify-center p-4 relative overflow-hidden">
      {/* خلفية عصرية متحركة */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-0 right-0 w-72 h-72 bg-cyan-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="w-full max-w-md relative z-10">
        {/* شعار الصفوة */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <Logo size="lg" variant="full" />
          </div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent mb-2">
            مرحباً بك في نظام الصفوة
          </h1>
          <p className="text-gray-600 font-medium">
            نظام إدارة موارد المؤسسة المتكامل
          </p>
          <div className="mt-4 flex justify-center">
            <div className="h-1 w-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
          </div>
        </div>

        {/* بطاقة تسجيل الدخول العصرية */}
        <Card className="bg-white/80 backdrop-blur-lg border-white/20 shadow-2xl">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-xl font-bold text-gray-800">تسجيل الدخول</CardTitle>
            <CardDescription className="text-gray-600">
              أدخل بياناتك للوصول إلى النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LoginForm />
          </CardContent>
        </Card>

        {/* معلومات الحسابات التجريبية */}
        <Card className="mt-6 bg-white/60 backdrop-blur-lg border-white/20">
          <CardContent className="p-4">
            <h3 className="text-sm font-semibold text-gray-700 mb-3 text-center">حسابات تجريبية:</h3>
            <div className="space-y-2 text-xs">
              <div className="flex justify-between items-center bg-blue-50 rounded-lg p-2">
                <span className="font-medium text-blue-800">مدير النظام:</span>
                <span className="text-blue-600"><EMAIL></span>
              </div>
              <div className="flex justify-between items-center bg-green-50 rounded-lg p-2">
                <span className="font-medium text-green-800">مشرف الإنتاج:</span>
                <span className="text-green-600"><EMAIL></span>
              </div>
              <div className="flex justify-between items-center bg-purple-50 rounded-lg p-2">
                <span className="font-medium text-purple-800">مشغل الماكينة:</span>
                <span className="text-purple-600"><EMAIL></span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>© 2024 شركة الصفوة للنسيج - جميع الحقوق محفوظة</p>
        </div>
      </div>
    </div>
  )
}
