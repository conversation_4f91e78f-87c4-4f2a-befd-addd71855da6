@echo off
echo.
echo Starting Safa Textile ERP System...
echo ====================================
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from: https://nodejs.org/
    echo Then restart Command Prompt and try again
    pause
    exit /b 1
)

echo Node.js found! Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating environment file...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo Environment file created successfully!
    ) else (
        echo WARNING: .env.example not found
    )
) else (
    echo Environment file already exists
)

echo.
echo Starting the system...
echo.
echo ====================================
echo System will be available at:
echo http://localhost:3000
echo ====================================
echo.
echo Demo Accounts:
echo - Admin: <EMAIL> / admin123
echo - Supervisor: <EMAIL> / super123  
echo - Operator: <EMAIL> / oper123
echo.
echo Press Ctrl+C to stop the system
echo ====================================
echo.

npm run dev
