@echo off
echo.
echo Password Update Tool - Safa Textile ERP
echo ========================================
echo.

echo [1/3] Updating password configuration...
node change-passwords.js

if %errorlevel% neq 0 (
    echo Error updating passwords
    pause
    exit /b 1
)

echo.
echo [2/3] Applying changes to database...
cmd /c "npx prisma db seed"

if %errorlevel% neq 0 (
    echo Error applying database changes
    pause
    exit /b 1
)

echo.
echo [3/3] Restarting system...
taskkill /F /IM node.exe 2>nul
timeout /t 3 /nobreak >nul
start cmd /c "npm run dev"

echo.
echo ========================================
echo NEW LOGIN CREDENTIALS:
echo ========================================
echo.
echo ADMIN:
echo Email: <EMAIL>
echo Password: safa2024
echo.
echo SUPERVISOR:
echo Email: <EMAIL>
echo Password: textile123
echo.
echo OPERATOR:
echo Email: <EMAIL>
echo Password: machine456
echo.
echo ========================================
echo System will be ready in a few seconds...
echo Open: http://localhost:3000
echo ========================================
echo.
pause
