import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Building2, CheckCircle, Clock, AlertTriangle } from 'lucide-react'

const stats = [
  {
    title: 'إجمالي الموردين',
    value: '24',
    change: '+3',
    changeType: 'positive' as const,
    icon: Building2,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    title: 'موردين نشطين',
    value: '18',
    change: '+2',
    changeType: 'positive' as const,
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    title: 'في الانتظار',
    value: '4',
    change: '+1',
    changeType: 'neutral' as const,
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
  },
  {
    title: 'متأخرين في التوريد',
    value: '2',
    change: '-1',
    changeType: 'negative' as const,
    icon: AlertTriangle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
  },
]

export function SuppliersStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-full ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : stat.changeType === 'negative'
                ? 'text-red-600'
                : 'text-muted-foreground'
            }`}>
              {stat.change} من الشهر الماضي
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
