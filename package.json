{"name": "textile-erp", "version": "1.0.0", "description": "نظام إدارة موارد المؤسسة للنسيج - Textile ERP System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "windows:install": "npm install && npm run db:generate", "windows:setup": "npm run db:migrate && npm run db:seed", "windows:start": "npm run dev", "test:setup": "node test-setup.js", "test:db": "node test-database.js"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.7.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.9", "@tanstack/react-table": "^8.11.6", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "next": "14.0.4", "next-auth": "^4.24.5", "next-intl": "^3.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-day-picker": "^8.10.0", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@hookform/resolvers": "^3.3.2", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "prisma": "^5.7.1", "tailwindcss": "^3.4.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["ERP", "textile", "manufacturing", "inventory", "production", "arabic", "nextjs", "typescript"], "author": "Textile ERP Team", "license": "MIT"}