'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Download,
  Filter,
  Eye,
  FileText,
  PieChart
} from 'lucide-react'

interface FinancialReport {
  id: string
  name: string
  type: string
  period: string
  generatedDate: Date
  status: string
  description: string
  data: any
}

const mockReports: FinancialReport[] = [
  {
    id: '1',
    name: 'قائمة الدخل - يناير 2024',
    type: 'INCOME_STATEMENT',
    period: '2024-01',
    generatedDate: new Date('2024-01-31'),
    status: 'COMPLETED',
    description: 'قائمة الدخل الشهرية لشهر يناير 2024',
    data: {
      revenue: 150000,
      expenses: 120000,
      netIncome: 30000,
      grossProfit: 45000
    }
  },
  {
    id: '2',
    name: 'الميزانية العمومية - يناير 2024',
    type: 'BALANCE_SHEET',
    period: '2024-01',
    generatedDate: new Date('2024-01-31'),
    status: 'COMPLETED',
    description: 'الميزانية العمومية كما في نهاية يناير 2024',
    data: {
      totalAssets: 500000,
      totalLiabilities: 200000,
      totalEquity: 300000
    }
  },
  {
    id: '3',
    name: 'تقرير التدفق النقدي - يناير 2024',
    type: 'CASH_FLOW',
    period: '2024-01',
    generatedDate: new Date('2024-01-31'),
    status: 'COMPLETED',
    description: 'تقرير التدفق النقدي لشهر يناير 2024',
    data: {
      operatingCashFlow: 25000,
      investingCashFlow: -15000,
      financingCashFlow: 5000,
      netCashFlow: 15000
    }
  },
  {
    id: '4',
    name: 'تقرير المبيعات - يناير 2024',
    type: 'SALES_REPORT',
    period: '2024-01',
    generatedDate: new Date('2024-01-31'),
    status: 'COMPLETED',
    description: 'تقرير مفصل عن المبيعات لشهر يناير 2024',
    data: {
      totalSales: 150000,
      salesCount: 45,
      averageSale: 3333,
      topProduct: 'قميص قطني أزرق'
    }
  },
  {
    id: '5',
    name: 'تقرير المشتريات - يناير 2024',
    type: 'PURCHASE_REPORT',
    period: '2024-01',
    generatedDate: new Date('2024-01-31'),
    status: 'COMPLETED',
    description: 'تقرير مفصل عن المشتريات لشهر يناير 2024',
    data: {
      totalPurchases: 80000,
      purchaseCount: 25,
      averagePurchase: 3200,
      topSupplier: 'مؤسسة المواد الخام'
    }
  }
]

const getReportTypeColor = (type: string) => {
  switch (type) {
    case 'INCOME_STATEMENT': return 'bg-green-500'
    case 'BALANCE_SHEET': return 'bg-blue-500'
    case 'CASH_FLOW': return 'bg-purple-500'
    case 'SALES_REPORT': return 'bg-orange-500'
    case 'PURCHASE_REPORT': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getReportTypeText = (type: string) => {
  switch (type) {
    case 'INCOME_STATEMENT': return 'قائمة الدخل'
    case 'BALANCE_SHEET': return 'الميزانية العمومية'
    case 'CASH_FLOW': return 'التدفق النقدي'
    case 'SALES_REPORT': return 'تقرير المبيعات'
    case 'PURCHASE_REPORT': return 'تقرير المشتريات'
    default: return 'غير محدد'
  }
}

const getReportTypeIcon = (type: string) => {
  switch (type) {
    case 'INCOME_STATEMENT': return <TrendingUp className="h-4 w-4" />
    case 'BALANCE_SHEET': return <BarChart3 className="h-4 w-4" />
    case 'CASH_FLOW': return <DollarSign className="h-4 w-4" />
    case 'SALES_REPORT': return <TrendingUp className="h-4 w-4" />
    case 'PURCHASE_REPORT': return <TrendingDown className="h-4 w-4" />
    default: return <FileText className="h-4 w-4" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'bg-green-500'
    case 'PENDING': return 'bg-yellow-500'
    case 'FAILED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'مكتمل'
    case 'PENDING': return 'قيد الإنشاء'
    case 'FAILED': return 'فشل'
    default: return 'غير محدد'
  }
}

export default function FinancialReportsPage() {
  const [reports, setReports] = useState<FinancialReport[]>(mockReports)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedPeriod, setSelectedPeriod] = useState<string>('all')

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || report.type === selectedType
    const matchesPeriod = selectedPeriod === 'all' || report.period === selectedPeriod
    return matchesSearch && matchesType && matchesPeriod
  })

  // Calculate summary data
  const totalRevenue = reports
    .filter(r => r.type === 'INCOME_STATEMENT')
    .reduce((acc, r) => acc + (r.data.revenue || 0), 0)

  const totalExpenses = reports
    .filter(r => r.type === 'INCOME_STATEMENT')
    .reduce((acc, r) => acc + (r.data.expenses || 0), 0)

  const netIncome = totalRevenue - totalExpenses

  const totalAssets = reports
    .filter(r => r.type === 'BALANCE_SHEET')
    .reduce((acc, r) => acc + (r.data.totalAssets || 0), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">التقارير المالية</h1>
          <p className="text-gray-600">تقارير مالية شاملة ومفصلة</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Calendar className="ml-2 h-4 w-4" />
            إنشاء تقرير
          </Button>
          <Button>
            <Download className="ml-2 h-4 w-4" />
            تصدير جميع التقارير
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-green-600">
                  {totalRevenue.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
                <p className="text-2xl font-bold text-red-600">
                  {totalExpenses.toLocaleString()} ر.س
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">صافي الربح</p>
                <p className={`text-2xl font-bold ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {netIncome.toLocaleString()} ر.س
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الأصول</p>
                <p className="text-2xl font-bold text-blue-600">
                  {totalAssets.toLocaleString()} ر.س
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle>إنشاء تقرير سريع</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
              <TrendingUp className="h-8 w-8 mb-2 text-green-600" />
              <span>قائمة الدخل</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
              <BarChart3 className="h-8 w-8 mb-2 text-blue-600" />
              <span>الميزانية العمومية</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
              <DollarSign className="h-8 w-8 mb-2 text-purple-600" />
              <span>التدفق النقدي</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
              <TrendingUp className="h-8 w-8 mb-2 text-orange-600" />
              <span>تقرير المبيعات</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center p-6 h-auto">
              <TrendingDown className="h-8 w-8 mb-2 text-red-600" />
              <span>تقرير المشتريات</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FileText className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في التقارير..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <div className="flex space-x-2 space-x-reverse">
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الأنواع</option>
                <option value="INCOME_STATEMENT">قائمة الدخل</option>
                <option value="BALANCE_SHEET">الميزانية العمومية</option>
                <option value="CASH_FLOW">التدفق النقدي</option>
                <option value="SALES_REPORT">تقرير المبيعات</option>
                <option value="PURCHASE_REPORT">تقرير المشتريات</option>
              </select>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">جميع الفترات</option>
                <option value="2024-01">يناير 2024</option>
                <option value="2023-12">ديسمبر 2023</option>
                <option value="2023-11">نوفمبر 2023</option>
              </select>
              <Button variant="outline">
                <Filter className="ml-2 h-4 w-4" />
                تصفية متقدمة
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle>التقارير المالية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredReports.map((report) => (
              <div key={report.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getReportTypeColor(report.type)}>
                      {getReportTypeIcon(report.type)}
                      <span className="mr-1">{getReportTypeText(report.type)}</span>
                    </Badge>
                    <Badge className={getStatusColor(report.status)}>
                      {getStatusText(report.status)}
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{report.name}</h3>
                      <p className="text-sm text-gray-600">{report.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 ml-1" />
                      تحميل PDF
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 ml-1" />
                      تحميل Excel
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">الفترة</p>
                    <p className="font-medium">{report.period}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">تاريخ الإنشاء</p>
                    <p className="font-medium">{report.generatedDate.toLocaleDateString('ar-SA')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">النوع</p>
                    <p className="font-medium">{getReportTypeText(report.type)}</p>
                  </div>
                </div>

                {/* Report Data Summary */}
                <div className="mt-3 p-3 bg-gray-100 rounded">
                  <p className="text-sm font-medium text-gray-700 mb-2">ملخص البيانات:</p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    {report.type === 'INCOME_STATEMENT' && (
                      <>
                        <div>
                          <span className="text-gray-600">الإيرادات: </span>
                          <span className="font-medium text-green-600">
                            {report.data.revenue?.toLocaleString()} ر.س
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">المصروفات: </span>
                          <span className="font-medium text-red-600">
                            {report.data.expenses?.toLocaleString()} ر.س
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">صافي الربح: </span>
                          <span className="font-medium text-blue-600">
                            {report.data.netIncome?.toLocaleString()} ر.س
                          </span>
                        </div>
                      </>
                    )}
                    {report.type === 'BALANCE_SHEET' && (
                      <>
                        <div>
                          <span className="text-gray-600">الأصول: </span>
                          <span className="font-medium text-blue-600">
                            {report.data.totalAssets?.toLocaleString()} ر.س
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">الخصوم: </span>
                          <span className="font-medium text-red-600">
                            {report.data.totalLiabilities?.toLocaleString()} ر.س
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">حقوق الملكية: </span>
                          <span className="font-medium text-green-600">
                            {report.data.totalEquity?.toLocaleString()} ر.س
                          </span>
                        </div>
                      </>
                    )}
                    {report.type === 'SALES_REPORT' && (
                      <>
                        <div>
                          <span className="text-gray-600">إجمالي المبيعات: </span>
                          <span className="font-medium text-green-600">
                            {report.data.totalSales?.toLocaleString()} ر.س
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">عدد المبيعات: </span>
                          <span className="font-medium">{report.data.salesCount}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">متوسط البيع: </span>
                          <span className="font-medium">
                            {report.data.averageSale?.toLocaleString()} ر.س
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {filteredReports.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد تقارير تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
