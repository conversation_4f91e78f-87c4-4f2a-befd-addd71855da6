@echo off
echo.
echo Fixing Login Database Issues
echo ============================
echo.

echo [1/5] Stopping any running processes...
taskkill /F /IM node.exe 2>nul
timeout /t 2 /nobreak >nul

echo [2/5] Cleaning database...
if exist "data\database.db" del "data\database.db"
if not exist "data" mkdir data

echo [3/5] Generating new database schema...
cmd /c "node_modules\.bin\prisma generate"
if %errorlevel% neq 0 (
    echo Trying alternative method...
    node node_modules\prisma\build\index.js generate
)

echo [4/5] Creating database...
cmd /c "node_modules\.bin\prisma db push"
if %errorlevel% neq 0 (
    echo Trying alternative method...
    node node_modules\prisma\build\index.js db push
)

echo [5/5] Adding test users...
node prisma/seed-simple.js

echo.
echo ============================
echo DATABASE FIXED!
echo ============================
echo.
echo Login with USERNAME + PASSWORD:
echo - Admin: admin / admin123
echo - Supervisor: supervisor / super123
echo - Operator: operator / oper123
echo.
echo Starting system...
echo.

start http://localhost:3000
cmd /c "npm run dev"
