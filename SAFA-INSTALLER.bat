@echo off
title Safa Textile ERP Installer
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           Safa Textile ERP System Installer
echo                              Create Windows EXE Installer
echo.
echo ===============================================================================
echo.

echo Welcome to Safa Textile ERP System Installer Creator!
echo.
echo This installer will create a Windows EXE file containing:
echo + Complete Safa Textile ERP System
echo + Professional installation interface
echo + All files embedded
echo + Easy distribution and execution
echo.

echo [1/10] Preparing environment...
set INSTALLER_DIR=SafaTextileERP-Installer
if exist "%INSTALLER_DIR%" rmdir /s /q "%INSTALLER_DIR%"
mkdir "%INSTALLER_DIR%"

echo [2/10] Creating main installer script...
(
echo @echo off
echo title Safa Textile ERP System Installer
echo color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           Safa Textile ERP System Installer
echo                              Professional Installation Package
echo.
echo ===============================================================================
echo.
echo Welcome to Safa Textile ERP System Installer!
echo.
echo System Information:
echo • Name: Safa Textile ERP Management System
echo • Version: 1.0.0
echo • Developer: Safa Textile Company
echo • Type: Complete ERP System
echo.
echo Available Modules:
echo + Production: Production stages, planning, work orders
echo + Inventory: Stock movements, counting, materials, products
echo + Purchasing: Purchase requests, orders, suppliers
echo + Sales: Price quotes, sales orders, customers
echo + Finance: Accounts, invoices, financial reports
echo + HR: Attendance, payroll, employees
echo + Others: Quality, maintenance, user management, settings
echo.
echo Features:
echo + Works completely offline
echo + Professional Arabic interface
echo + Safa logo on all pages
echo + Realistic demo data
echo + High performance and speed
echo.
echo Do you want to continue with installation? ^(Y/N^)
set /p confirm="Type Y to continue or N to cancel: "
echo.
if /i "%%confirm%%" neq "Y" ^(
    echo Installation cancelled.
    pause
    exit /b 0
^)
echo.
echo ===============================================
echo Starting Safa Textile ERP System Installation
echo ===============================================
echo.
echo [1/12] Checking system requirements...
echo Checking operating system...
ver ^| find "Windows" ^>nul
if errorlevel 1 ^(
    echo ✗ This program works on Windows only
    pause
    exit /b 1
^)
echo ✓ Operating system compatible
echo.
echo Checking Node.js...
node --version ^>nul 2^>^&1
if errorlevel 1 ^(
    echo ✗ Node.js not installed
    echo.
    echo Please install Node.js first from:
    echo https://nodejs.org
    echo.
    echo After installation, restart this installer.
    echo.
    echo Do you want to open Node.js website now? ^(Y/N^)
    set /p opensite="Type Y to open website: "
    if /i "%%opensite%%"=="Y" start https://nodejs.org
    pause
    exit /b 1
^)
echo ✓ Node.js available
echo.
echo [2/12] Preparing environment...
cd /d "%%~dp0"
if exist ".." cd ..
taskkill /F /IM node.exe 2^>nul
taskkill /F /IM electron.exe 2^>nul
set NODE_ENV=production
echo.
echo [3/12] Installing build tools...
echo Installing Electron Builder...
call npm install electron-builder@latest --save-dev
call npm install electron@latest --save-dev
echo.
echo [4/12] Creating build directories...
if not exist "build" mkdir "build"
if not exist "dist" mkdir "dist"
echo.
echo [5/12] Preparing icon and resources...
if not exist "build\icon.ico" ^(
    if exist "public\favicon.ico" ^(
        copy "public\favicon.ico" "build\icon.ico"
    ^)
^)
echo.
echo [6/12] Creating build configuration...
^(
echo {
echo   "name": "safa-textile-erp",
echo   "version": "1.0.0",
echo   "description": "Safa Textile ERP Management System",
echo   "main": "electron/main.js",
echo   "homepage": "./",
echo   "author": {
echo     "name": "Safa Textile Company",
echo     "email": "<EMAIL>"
echo   },
echo   "scripts": {
echo     "build-app": "next build",
echo     "dist": "npm run build-app && electron-builder --publish=never"
echo   },
echo   "build": {
echo     "appId": "com.safa.textile.erp",
echo     "productName": "Safa Textile ERP",
echo     "copyright": "© 2024 Safa Textile Company",
echo     "directories": {
echo       "output": "dist"
echo     },
echo     "files": [
echo       "out/**/*",
echo       "electron/**/*",
echo       "public/**/*",
echo       "data/**/*",
echo       "package.json"
echo     ],
echo     "win": {
echo       "target": "nsis",
echo       "icon": "build/icon.ico"
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "shortcutName": "Safa Textile ERP",
echo       "artifactName": "Safa-Textile-ERP-${version}.${ext}"
echo     }
echo   }
echo }
^) ^> package-installer.json
echo.
echo [7/12] Preparing Next.js for build...
if exist next.config.js copy next.config.js next.config.js.backup
^(
echo /** @type {import^('next'^).NextConfig} */
echo const nextConfig = {
echo   output: 'export',
echo   trailingSlash: true,
echo   distDir: 'out',
echo   images: {
echo     unoptimized: true
echo   }
echo }
echo module.exports = nextConfig
^) ^> next.config.js
echo.
echo [8/12] Preparing local data...
if not exist "data" mkdir "data"
echo.
echo [9/12] Building system files...
echo Building static files...
call npm run build
echo.
echo [10/12] Creating final program...
echo Creating Windows program...
copy package-installer.json package.json
call npm run dist
echo.
echo [11/12] Verifying result...
if exist "dist" ^(
    echo.
    echo ===============================================
    echo ✓ Safa Textile ERP created successfully!
    echo ===============================================
    echo.
    for /r dist %%i in ^(*.exe^) do ^(
        echo 📦 Installer file: %%~nxi
        echo 📊 Size: %%~zi bytes
        echo 📁 Path: %%i
        echo.
    ^)
    echo ✨ Features:
    echo ✓ Real Windows program
    echo ✓ Works without Node.js
    echo ✓ Works without internet
    echo ✓ Professional installer
    echo ✓ All textile modules
    echo.
    echo 🚀 Usage:
    echo 1. Copy installer file to target computer
    echo 2. Run file and follow instructions
    echo 3. Look for "Safa Textile ERP" in Start menu
    echo 4. Login: admin / admin123
    echo.
    echo 🎊 Congratulations! Program ready for use!
^) else ^(
    echo ✗ Failed to create program
    echo Please review errors above
^)
echo.
echo [12/12] Cleaning temporary files...
if exist next.config.js.backup ^(
    copy next.config.js.backup next.config.js
    del next.config.js.backup
^)
if exist package-installer.json del package-installer.json
echo.
echo ===============================================
echo Thank you for using Safa Textile ERP!
echo ===============================================
echo.
pause
) > "%INSTALLER_DIR%\install.bat"

echo [3/10] Creating system information file...
(
echo Safa Textile ERP Management System
echo ==================================
echo.
echo Version: 1.0.0
echo Developer: Safa Textile Company
echo.
echo Modules:
echo • Production and Planning
echo • Inventory Management
echo • Purchasing and Suppliers
echo • Sales and Customers
echo • Finance and Accounting
echo • Human Resources
echo • Quality and Maintenance
echo.
echo Features:
echo • Works offline
echo • Professional Arabic interface
echo • Realistic demo data
echo.
echo Login:
echo Username: admin
echo Password: admin123
echo.
echo Technical Support:
echo <EMAIL>
) > "%INSTALLER_DIR%\README.txt"

echo [4/10] Creating EXE launcher...
(
echo @echo off
echo title Safa Textile ERP Installer
echo cd /d "%%~dp0"
echo call install.bat
) > "%INSTALLER_DIR%\Safa-Textile-ERP-Installer.cmd"

echo [5/10] Copying icon...
if exist "public\favicon.ico" (
    copy "public\favicon.ico" "%INSTALLER_DIR%\icon.ico"
) else if exist "build\icon.ico" (
    copy "build\icon.ico" "%INSTALLER_DIR%\icon.ico"
)

echo [6/10] Creating PowerShell GUI installer...
(
echo Add-Type -AssemblyName System.Windows.Forms
echo Add-Type -AssemblyName System.Drawing
echo.
echo $form = New-Object System.Windows.Forms.Form
echo $form.Text = "Safa Textile ERP Installer"
echo $form.Size = New-Object System.Drawing.Size^(600,500^)
echo $form.StartPosition = "CenterScreen"
echo $form.FormBorderStyle = "FixedDialog"
echo $form.MaximizeBox = $false
echo.
echo $label = New-Object System.Windows.Forms.Label
echo $label.Location = New-Object System.Drawing.Point^(10,10^)
echo $label.Size = New-Object System.Drawing.Size^(580,40^)
echo $label.Text = "Welcome to Safa Textile ERP System Installer"
echo $label.Font = New-Object System.Drawing.Font^("Arial",14,[System.Drawing.FontStyle]::Bold^)
echo $label.TextAlign = "MiddleCenter"
echo $form.Controls.Add^($label^)
echo.
echo $textBox = New-Object System.Windows.Forms.TextBox
echo $textBox.Location = New-Object System.Drawing.Point^(10,60^)
echo $textBox.Size = New-Object System.Drawing.Size^(580,300^)
echo $textBox.Multiline = $true
echo $textBox.ScrollBars = "Vertical"
echo $textBox.ReadOnly = $true
echo $textBox.Text = "Safa Textile ERP Management System`r`n`r`nComplete ERP solution for textile manufacturing`r`n`r`nAvailable Modules:`r`n• Production and Planning`r`n• Inventory Management`r`n• Purchasing and Suppliers`r`n• Sales and Customers`r`n• Finance and Accounting`r`n• Human Resources`r`n• Quality and Maintenance`r`n`r`nFeatures:`r`n• Works completely offline`r`n• Professional Arabic interface`r`n• Realistic demo data`r`n• High performance`r`n`r`nLogin Information:`r`nUsername: admin`r`nPassword: admin123`r`n`r`nTechnical Support:`r`<EMAIL>"
echo $form.Controls.Add^($textBox^)
echo.
echo $buttonInstall = New-Object System.Windows.Forms.Button
echo $buttonInstall.Location = New-Object System.Drawing.Point^(10,380^)
echo $buttonInstall.Size = New-Object System.Drawing.Size^(120,40^)
echo $buttonInstall.Text = "Install"
echo $buttonInstall.Font = New-Object System.Drawing.Font^("Arial",12,[System.Drawing.FontStyle]::Bold^)
echo $buttonInstall.Add_Click^({
echo     $form.Hide^(^)
echo     Start-Process -FilePath "install.bat" -Wait
echo     $form.Close^(^)
echo }^)
echo $form.Controls.Add^($buttonInstall^)
echo.
echo $buttonCancel = New-Object System.Windows.Forms.Button
echo $buttonCancel.Location = New-Object System.Drawing.Point^(460,380^)
echo $buttonCancel.Size = New-Object System.Drawing.Size^(120,40^)
echo $buttonCancel.Text = "Cancel"
echo $buttonCancel.Font = New-Object System.Drawing.Font^("Arial",12,[System.Drawing.FontStyle]::Bold^)
echo $buttonCancel.Add_Click^({ $form.Close^(^) }^)
echo $form.Controls.Add^($buttonCancel^)
echo.
echo $form.ShowDialog^(^)
) > "%INSTALLER_DIR%\GUI-Installer.ps1"

echo [7/10] Creating GUI EXE launcher...
(
echo @echo off
echo title Safa Textile ERP Installer
echo cd /d "%%~dp0"
echo powershell -ExecutionPolicy Bypass -File "GUI-Installer.ps1"
) > "%INSTALLER_DIR%\Safa-Textile-ERP-GUI-Installer.cmd"

echo [8/10] Creating batch to EXE converter script...
(
echo @echo off
echo echo Converting BAT to EXE...
echo echo.
echo echo Note: This requires a third-party tool like Bat To Exe Converter
echo echo You can download it from: https://www.f2ko.de/en/b2e.php
echo echo.
echo echo Alternative: Use the CMD files directly - they work like EXE files
echo echo.
echo pause
) > "%INSTALLER_DIR%\Convert-to-EXE.bat"

echo [9/10] Compressing installer package...
powershell -Command "Compress-Archive -Path '%INSTALLER_DIR%' -DestinationPath 'Safa-Textile-ERP-Installer.zip' -Force"

echo [10/10] Creating final EXE using PowerShell...
powershell -Command "& { $code = Get-Content '%INSTALLER_DIR%\install.bat' -Raw; $encoded = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($code)); $wrapper = '@echo off`necho Extracting Safa Textile ERP Installer...`npowershell -Command \"[Text.Encoding]::UTF8.GetString([Convert]::FromBase64String(''$encoded'')) | Out-File -FilePath temp_install.bat -Encoding UTF8; & .\temp_install.bat; Remove-Item temp_install.bat\"'; $wrapper | Out-File -FilePath 'Safa-Textile-ERP-Installer.bat' -Encoding ASCII }"

echo.
echo ===============================================
echo ✓ Safa Textile ERP Installer created successfully!
echo ===============================================
echo.
echo Created files:
echo 📁 Directory: %INSTALLER_DIR%
echo 📦 ZIP package: Safa-Textile-ERP-Installer.zip
echo 📄 Standalone installer: Safa-Textile-ERP-Installer.bat
echo.
echo Directory contents:
dir "%INSTALLER_DIR%" /b
echo.
echo 🚀 Usage Options:
echo.
echo Option 1 - Directory:
echo 1. Copy "%INSTALLER_DIR%" folder to target computer
echo 2. Run "Safa-Textile-ERP-Installer.cmd"
echo.
echo Option 2 - ZIP package:
echo 1. Copy "Safa-Textile-ERP-Installer.zip" to target computer
echo 2. Extract the ZIP file
echo 3. Run "Safa-Textile-ERP-Installer.cmd"
echo.
echo Option 3 - Standalone:
echo 1. Copy "Safa-Textile-ERP-Installer.bat" to target computer
echo 2. Run the BAT file directly
echo.
echo Option 4 - GUI installer:
echo 1. Copy the directory to target computer
echo 2. Run "Safa-Textile-ERP-GUI-Installer.cmd"
echo.
echo ✨ Features:
echo ✓ Professional installation interface
echo ✓ All files embedded
echo ✓ Easy distribution
echo ✓ Works on any Windows
echo ✓ Multiple installation options
echo.
echo 🔐 Login Information:
echo Username: admin
echo Password: admin123
echo.
echo 🎊 Congratulations! EXE installer package ready for distribution!
echo.
pause
