'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { 
  ClipboardList,
  Package,
  AlertTriangle,
  CheckCircle,
  Plus,
  Search,
  Download,
  Calculator,
  Eye,
  Edit
} from 'lucide-react'

interface InventoryCountItem {
  id: string
  itemCode: string
  itemName: string
  category: string
  unit: string
  systemQuantity: number
  countedQuantity?: number
  variance?: number
  unitPrice: number
  totalValue: number
  location: string
  status: string
  countedBy?: string
  countedAt?: Date
  notes?: string
}

interface InventoryCount {
  id: string
  countNumber: string
  description: string
  status: string
  startDate: Date
  endDate?: Date
  createdBy: string
  items: InventoryCountItem[]
  totalItems: number
  countedItems: number
  totalVariance: number
}

const mockCountItems: InventoryCountItem[] = [
  {
    id: '1',
    itemCode: 'MAT001',
    itemName: 'قطن خام عالي الجودة',
    category: 'مواد خام',
    unit: 'كيلو',
    systemQuantity: 2500,
    countedQuantity: 2475,
    variance: -25,
    unitPrice: 15.50,
    totalValue: 38375,
    location: 'مخزن المواد الخام - رف أ1',
    status: 'COUNTED',
    countedBy: 'أحمد محمد',
    countedAt: new Date('2024-01-15T10:30:00'),
    notes: 'نقص طفيف في الكمية'
  },
  {
    id: '2',
    itemCode: 'MAT002',
    itemName: 'صبغة زرقاء',
    category: 'مواد خام',
    unit: 'لتر',
    systemQuantity: 250,
    countedQuantity: 248,
    variance: -2,
    unitPrice: 45.00,
    totalValue: 11160,
    location: 'مخزن الكيماويات - رف ب2',
    status: 'COUNTED',
    countedBy: 'فاطمة علي',
    countedAt: new Date('2024-01-15T11:15:00')
  },
  {
    id: '3',
    itemCode: 'PRD001',
    itemName: 'قميص قطني أزرق',
    category: 'منتجات نهائية',
    unit: 'قطعة',
    systemQuantity: 150,
    countedQuantity: 155,
    variance: 5,
    unitPrice: 75.00,
    totalValue: 11625,
    location: 'مخزن المنتجات - رف ج1',
    status: 'COUNTED',
    countedBy: 'محمد أحمد',
    countedAt: new Date('2024-01-15T14:20:00'),
    notes: 'زيادة في الكمية - تحقق من السجلات'
  },
  {
    id: '4',
    itemCode: 'MAT003',
    itemName: 'صبغة حمراء',
    category: 'مواد خام',
    unit: 'لتر',
    systemQuantity: 180,
    unitPrice: 42.00,
    totalValue: 7560,
    location: 'مخزن الكيماويات - رف ب3',
    status: 'PENDING'
  },
  {
    id: '5',
    itemCode: 'PRD002',
    itemName: 'قميص قطني أحمر',
    category: 'منتجات نهائية',
    unit: 'قطعة',
    systemQuantity: 120,
    unitPrice: 78.00,
    totalValue: 9360,
    location: 'مخزن المنتجات - رف ج2',
    status: 'PENDING'
  }
]

const mockCount: InventoryCount = {
  id: '1',
  countNumber: 'INV-COUNT-2024-001',
  description: 'جرد شهري - يناير 2024',
  status: 'IN_PROGRESS',
  startDate: new Date('2024-01-15T08:00:00'),
  createdBy: 'مدير المخزون',
  items: mockCountItems,
  totalItems: mockCountItems.length,
  countedItems: mockCountItems.filter(item => item.status === 'COUNTED').length,
  totalVariance: mockCountItems
    .filter(item => item.variance !== undefined)
    .reduce((acc, item) => acc + (item.variance || 0), 0)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'COUNTED': return 'bg-green-500'
    case 'PENDING': return 'bg-yellow-500'
    case 'VARIANCE': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'COUNTED': return 'تم العد'
    case 'PENDING': return 'في الانتظار'
    case 'VARIANCE': return 'يوجد فرق'
    default: return 'غير محدد'
  }
}

export default function InventoryCountPage() {
  const [count, setCount] = useState<InventoryCount>(mockCount)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [editingItem, setEditingItem] = useState<string | null>(null)
  const [countValue, setCountValue] = useState<string>('')

  const filteredItems = count.items.filter(item => {
    const matchesSearch = item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.itemCode.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const completionRate = (count.countedItems / count.totalItems) * 100

  const handleStartCount = (itemId: string) => {
    setEditingItem(itemId)
    const item = count.items.find(i => i.id === itemId)
    setCountValue(item?.systemQuantity.toString() || '')
  }

  const handleSaveCount = (itemId: string) => {
    const countedQuantity = parseFloat(countValue)
    if (isNaN(countedQuantity)) return

    setCount(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === itemId) {
          const variance = countedQuantity - item.systemQuantity
          return {
            ...item,
            countedQuantity,
            variance,
            status: 'COUNTED',
            countedBy: 'المستخدم الحالي',
            countedAt: new Date()
          }
        }
        return item
      }),
      countedItems: prev.items.filter(i => i.id === itemId || i.status === 'COUNTED').length + 1,
      totalVariance: prev.items
        .map(i => i.id === itemId ? countedQuantity - i.systemQuantity : (i.variance || 0))
        .reduce((acc, v) => acc + v, 0)
    }))

    setEditingItem(null)
    setCountValue('')
  }

  const categories = Array.from(new Set(count.items.map(item => item.category)))

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">جرد المخزون</h1>
          <p className="text-gray-600">إدارة عمليات جرد المخزون والتحقق من الكميات</p>
        </div>
        <div className="flex space-x-2 space-x-reverse">
          <Button variant="outline">
            <Download className="ml-2 h-4 w-4" />
            تصدير النتائج
          </Button>
          <Button>
            <Plus className="ml-2 h-4 w-4" />
            جرد جديد
          </Button>
        </div>
      </div>

      {/* Count Info Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{count.description}</CardTitle>
              <p className="text-gray-600">رقم الجرد: {count.countNumber}</p>
            </div>
            <Badge className={count.status === 'IN_PROGRESS' ? 'bg-blue-500' : 'bg-green-500'}>
              {count.status === 'IN_PROGRESS' ? 'قيد التنفيذ' : 'مكتمل'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-600">تاريخ البداية</p>
              <p className="font-medium">{count.startDate.toLocaleDateString('ar-SA')}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">المنشئ</p>
              <p className="font-medium">{count.createdBy}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">إجمالي الأصناف</p>
              <p className="font-medium">{count.totalItems}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">الأصناف المعدودة</p>
              <p className="font-medium">{count.countedItems}</p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>نسبة الإنجاز</span>
              <span>{completionRate.toFixed(1)}%</span>
            </div>
            <Progress value={completionRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الأصناف</p>
                <p className="text-2xl font-bold text-blue-600">{count.totalItems}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تم العد</p>
                <p className="text-2xl font-bold text-green-600">{count.countedItems}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {count.totalItems - count.countedItems}
                </p>
              </div>
              <ClipboardList className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الفروق</p>
                <p className={`text-2xl font-bold ${count.totalVariance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {count.totalVariance >= 0 ? '+' : ''}{count.totalVariance}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="البحث في الأصناف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع الفئات</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Count Items */}
      <Card>
        <CardHeader>
          <CardTitle>أصناف الجرد</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredItems.map((item) => (
              <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(item.status)}>
                      {getStatusText(item.status)}
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{item.itemName}</h3>
                      <p className="text-sm text-gray-600">كود: {item.itemCode} | {item.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {item.status === 'PENDING' && (
                      <Button 
                        size="sm"
                        onClick={() => handleStartCount(item.id)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Calculator className="h-4 w-4 ml-1" />
                        عد
                      </Button>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">الكمية بالنظام</p>
                    <p className="font-medium">{item.systemQuantity} {item.unit}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الكمية المعدودة</p>
                    {editingItem === item.id ? (
                      <div className="flex space-x-2 space-x-reverse">
                        <Input
                          type="number"
                          value={countValue}
                          onChange={(e) => setCountValue(e.target.value)}
                          className="w-20"
                          placeholder="0"
                        />
                        <Button size="sm" onClick={() => handleSaveCount(item.id)}>
                          حفظ
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => setEditingItem(null)}>
                          إلغاء
                        </Button>
                      </div>
                    ) : (
                      <p className="font-medium">
                        {item.countedQuantity !== undefined ? `${item.countedQuantity} ${item.unit}` : '-'}
                      </p>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الفرق</p>
                    <p className={`font-medium ${
                      item.variance === undefined ? 'text-gray-500' :
                      item.variance > 0 ? 'text-green-600' :
                      item.variance < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {item.variance !== undefined ? 
                        `${item.variance >= 0 ? '+' : ''}${item.variance} ${item.unit}` : '-'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الموقع</p>
                    <p className="font-medium text-xs">{item.location}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">القيمة</p>
                    <p className="font-medium">{item.totalValue.toLocaleString()} ر.س</p>
                  </div>
                </div>

                {item.countedBy && (
                  <div className="text-sm text-gray-600">
                    تم العد بواسطة: {item.countedBy} في {item.countedAt?.toLocaleDateString('ar-SA')}
                  </div>
                )}

                {item.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {item.notes}
                  </div>
                )}
              </div>
            ))}

            {filteredItems.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                لا توجد أصناف تطابق معايير البحث
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
