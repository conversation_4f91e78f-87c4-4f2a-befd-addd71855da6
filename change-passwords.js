// تغيير كلمات المرور - نظام الصفوة للنسيج
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// كلمات المرور الجديدة المقترحة
const newPasswords = {
  admin: 'safa2024',        // كلمة مرور قوية للمدير
  supervisor: 'textile123', // كلمة مرور للمشرف
  operator: 'machine456'    // كلمة مرور للمشغل
};

// أو يمكنك تخصيص كلمات مرور أخرى
const customPasswords = {
  admin: 'الصفوة123',      // كلمة مرور عربية
  supervisor: 'انتاج2024',  // كلمة مرور عربية
  operator: 'ماكينة789'     // كلمة مرور عربية
};

async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

async function updateUserPasswords() {
  console.log('🔐 تحديث كلمات المرور...');
  console.log('================================');
  
  try {
    // إنشاء كلمات مرور مشفرة
    const hashedPasswords = {};
    
    // استخدام كلمات المرور الجديدة
    for (const [user, password] of Object.entries(newPasswords)) {
      hashedPasswords[user] = await hashPassword(password);
      console.log(`✅ تم تشفير كلمة مرور ${user}: ${password}`);
    }
    
    // إنشاء ملف seed محدث
    const seedContent = `
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 إضافة البيانات التجريبية...');

  // إنشاء المستخدمين مع كلمات المرور الجديدة
  const users = [
    {
      id: '1',
      name: 'مدير النظام',
      email: '<EMAIL>',
      password: '${hashedPasswords.admin}',
      role: 'ADMIN',
      department: 'الإدارة',
      isActive: true,
    },
    {
      id: '2', 
      name: 'مشرف الإنتاج',
      email: '<EMAIL>',
      password: '${hashedPasswords.supervisor}',
      role: 'SUPERVISOR',
      department: 'الإنتاج',
      isActive: true,
    },
    {
      id: '3',
      name: 'مشغل الماكينة',
      email: '<EMAIL>', 
      password: '${hashedPasswords.operator}',
      role: 'OPERATOR',
      department: 'الإنتاج',
      isActive: true,
    },
  ];

  // حذف البيانات الموجودة
  await prisma.user.deleteMany({});
  
  // إضافة المستخدمين الجدد
  for (const user of users) {
    await prisma.user.create({
      data: user,
    });
    console.log(\`✅ تم إنشاء المستخدم: \${user.name}\`);
  }

  console.log('🎉 تم تحديث جميع كلمات المرور بنجاح!');
}

main()
  .catch((e) => {
    console.error('❌ خطأ في تحديث البيانات:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
`;

    // كتابة ملف seed جديد
    const seedPath = path.join(__dirname, 'prisma', 'seed.ts');
    fs.writeFileSync(seedPath, seedContent);
    
    console.log('');
    console.log('✅ تم إنشاء ملف seed جديد');
    console.log('');
    console.log('🔐 كلمات المرور الجديدة:');
    console.log('================================');
    console.log('👑 مدير النظام:');
    console.log('   📧 البريد: <EMAIL>');
    console.log(\`   🔑 كلمة المرور: \${newPasswords.admin}\`);
    console.log('');
    console.log('👨‍💼 مشرف الإنتاج:');
    console.log('   📧 البريد: <EMAIL>');
    console.log(\`   🔑 كلمة المرور: \${newPasswords.supervisor}\`);
    console.log('');
    console.log('👷‍♂️ مشغل الماكينة:');
    console.log('   📧 البريد: <EMAIL>');
    console.log(\`   🔑 كلمة المرور: \${newPasswords.operator}\`);
    console.log('');
    console.log('🚀 لتطبيق التغييرات، شغل الأمر التالي:');
    console.log('   npm run db:seed');
    
  } catch (error) {
    console.error('❌ خطأ في تحديث كلمات المرور:', error);
  }
}

// تشغيل التحديث
updateUserPasswords();
