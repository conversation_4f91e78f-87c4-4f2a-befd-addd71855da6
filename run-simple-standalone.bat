@echo off
title نظام الصفوة المستقل المبسط
color 0A
echo.
echo  ███████╗ █████╗ ███████╗ █████╗     ████████╗███████╗██╗  ██╗████████╗██╗██╗     ███████╗
echo  ██╔════╝██╔══██╗██╔════╝██╔══██╗    ╚══██╔══╝██╔════╝╚██╗██╔╝╚══██╔══╝██║██║     ██╔════╝
echo  ███████╗███████║█████╗  ███████║       ██║   █████╗   ╚███╔╝    ██║   ██║██║     █████╗  
echo  ╚════██║██╔══██║██╔══╝  ██╔══██║       ██║   ██╔══╝   ██╔██╗    ██║   ██║██║     ██╔══╝  
echo  ███████║██║  ██║██║     ██║  ██║       ██║   ███████╗██╔╝ ██╗   ██║   ██║███████╗███████╗
echo  ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝  ╚═╝       ╚═╝   ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝╚══════╝╚══════╝
echo.
echo                           نظام الصفوة للنسيج - التطبيق المستقل المبسط
echo                              يعمل بدون خادم - بدون إنترنت
echo.
echo ===============================================================================
echo.

echo [1/5] إيقاف العمليات السابقة...
taskkill /F /IM node.exe 2>nul
taskkill /F /IM electron.exe 2>nul

echo [2/5] التحقق من Electron...
npm list electron >nul 2>&1
if errorlevel 1 (
    echo تثبيت Electron...
    npm install electron --save-dev
) else (
    echo ✓ Electron مثبت
)

echo [3/5] تحضير البيئة...
set NODE_ENV=production
set STANDALONE_MODE=true

echo [4/5] تحضير البيانات...
if not exist "data" mkdir "data"

echo [5/5] تشغيل التطبيق المستقل...
echo.
echo ===============================================
echo نظام الصفوة للنسيج - التطبيق المستقل
echo ===============================================
echo.
echo المميزات:
echo ✓ يعمل بدون خادم ويب
echo ✓ يعمل بدون إنترنت
echo ✓ بيانات مدمجة ومحلية
echo ✓ أداء فائق وسرعة عالية
echo ✓ واجهة سطح مكتب احترافية
echo.
echo الوحدات المتاحة:
echo • الإنتاج: مراحل الإنتاج، تخطيط الإنتاج، أوامر العمل
echo • المخزون: حركة المخزون، الجرد، المواد، المنتجات
echo • المشتريات: طلبات الشراء، أوامر الشراء، الموردين
echo • المبيعات: عروض الأسعار، أوامر البيع، العملاء
echo • المالية: الحسابات، الفواتير، التقارير المالية
echo • الموارد البشرية: الحضور والانصراف، الرواتب، الموظفين
echo • أخرى: الجودة، الصيانة، إدارة المستخدمين، الإعدادات
echo.
echo تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ===============================================================================
echo.
echo جاري تشغيل التطبيق المستقل...
echo.

echo ملاحظة: هذا الإصدار يعمل مع الملفات الحالية بدون بناء معقد
echo.

npx electron .
