@echo off
echo.
echo Checking Safa Textile ERP System Setup...
echo ==========================================
echo.

echo [1/3] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    node --version
    echo Node.js is installed!
    set NODE_OK=1
) else (
    echo Node.js is NOT installed
    echo Please install Node.js from: https://nodejs.org/
    set NODE_OK=0
)

echo.
echo [2/3] Checking npm...
if %NODE_OK%==1 (
    npm --version >nul 2>&1
    if %errorlevel% equ 0 (
        npm --version
        echo npm is available!
        set NPM_OK=1
    ) else (
        echo npm is NOT available
        set NPM_OK=0
    )
) else (
    echo Skipping npm check (Node.js not available)
    set NPM_OK=0
)

echo.
echo [3/3] Checking project files...
if exist "package.json" (
    echo package.json found!
    set PROJECT_OK=1
) else (
    echo package.json NOT found
    echo Make sure you are in the correct project directory
    set PROJECT_OK=0
)

echo.
echo ==========================================
echo Summary:
echo ==========================================
if %NODE_OK%==1 (
    echo Node.js: Ready
) else (
    echo Node.js: NOT Ready
)

if %NPM_OK%==1 (
    echo npm: Ready
) else (
    echo npm: NOT Ready
)

if %PROJECT_OK%==1 (
    echo Project files: Ready
) else (
    echo Project files: NOT Ready
)

echo.
if %NODE_OK%==1 if %NPM_OK%==1 if %PROJECT_OK%==1 (
    echo System is READY to run!
    echo.
    echo Next steps:
    echo 1. npm install
    echo 2. copy .env.example .env
    echo 3. npm run dev
    echo.
    echo Would you like to run these steps now? (y/n)
    set /p choice=
    if "%choice%"=="y" (
        echo.
        echo Installing dependencies...
        npm install
        
        echo.
        echo Creating environment file...
        if not exist ".env" (
            copy ".env.example" ".env"
            echo Environment file created!
        )
        
        echo.
        echo Starting the system...
        echo System will be available at: http://localhost:3000
        echo.
        echo Demo accounts:
        echo - Admin: <EMAIL> / admin123
        echo - Supervisor: <EMAIL> / super123
        echo - Operator: <EMAIL> / oper123
        echo.
        echo Press Ctrl+C to stop the system
        echo.
        npm run dev
    )
) else (
    echo System is NOT ready!
    echo Please complete the installation first.
)

echo.
pause
