'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Play, 
  Pause, 
  CheckCircle, 
  Clock, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON>ting<PERSON>,
  Eye,
  Edit
} from 'lucide-react'

interface ProductionStage {
  id: string
  workOrderId: string
  workOrderNumber: string
  productName: string
  stage: string
  stageName: string
  status: string
  progress: number
  startTime?: Date
  endTime?: Date
  estimatedDuration: number
  actualDuration?: number
  operator: string
  machine: string
  notes?: string
}

const mockStages: ProductionStage[] = [
  {
    id: '1',
    workOrderId: 'WO-001',
    workOrderNumber: 'WO-2024-001',
    productName: 'قميص قطني أزرق',
    stage: 'SPINNING',
    stageName: 'الغزل',
    status: 'COMPLETED',
    progress: 100,
    startTime: new Date('2024-01-15T08:00:00'),
    endTime: new Date('2024-01-15T16:00:00'),
    estimatedDuration: 8,
    actualDuration: 8,
    operator: 'أحمد محمد',
    machine: 'ماكينة غزل رقم 1',
    notes: 'تم الانتهاء بنجاح'
  },
  {
    id: '2',
    workOrderId: 'WO-001',
    workOrderNumber: 'WO-2024-001',
    productName: 'قميص قطني أزرق',
    stage: 'WEAVING',
    stageName: 'النسيج',
    status: 'IN_PROGRESS',
    progress: 65,
    startTime: new Date('2024-01-16T08:00:00'),
    estimatedDuration: 10,
    operator: 'فاطمة علي',
    machine: 'نول رقم 3',
    notes: 'العمل يسير بشكل طبيعي'
  },
  {
    id: '3',
    workOrderId: 'WO-001',
    workOrderNumber: 'WO-2024-001',
    productName: 'قميص قطني أزرق',
    stage: 'DYEING',
    stageName: 'الصباغة',
    status: 'PENDING',
    progress: 0,
    estimatedDuration: 6,
    operator: 'محمد أحمد',
    machine: 'حوض صباغة رقم 2'
  },
  {
    id: '4',
    workOrderId: 'WO-002',
    workOrderNumber: 'WO-2024-002',
    productName: 'قميص قطني أحمر',
    stage: 'SPINNING',
    stageName: 'الغزل',
    status: 'IN_PROGRESS',
    progress: 30,
    startTime: new Date('2024-01-16T10:00:00'),
    estimatedDuration: 8,
    operator: 'سارة محمود',
    machine: 'ماكينة غزل رقم 2'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'bg-green-500'
    case 'IN_PROGRESS': return 'bg-blue-500'
    case 'PENDING': return 'bg-gray-500'
    case 'FAILED': return 'bg-red-500'
    default: return 'bg-gray-500'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED': return 'مكتمل'
    case 'IN_PROGRESS': return 'قيد التنفيذ'
    case 'PENDING': return 'في الانتظار'
    case 'FAILED': return 'فشل'
    default: return 'غير محدد'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'COMPLETED': return <CheckCircle className="h-4 w-4" />
    case 'IN_PROGRESS': return <Play className="h-4 w-4" />
    case 'PENDING': return <Clock className="h-4 w-4" />
    case 'FAILED': return <AlertTriangle className="h-4 w-4" />
    default: return <Clock className="h-4 w-4" />
  }
}

export default function ProductionStagesPage() {
  const [stages, setStages] = useState<ProductionStage[]>(mockStages)
  const [selectedStage, setSelectedStage] = useState<ProductionStage | null>(null)

  const handleStartStage = (stageId: string) => {
    setStages(stages.map(stage => 
      stage.id === stageId 
        ? { ...stage, status: 'IN_PROGRESS', startTime: new Date() }
        : stage
    ))
  }

  const handlePauseStage = (stageId: string) => {
    setStages(stages.map(stage => 
      stage.id === stageId 
        ? { ...stage, status: 'PENDING' }
        : stage
    ))
  }

  const handleCompleteStage = (stageId: string) => {
    setStages(stages.map(stage => 
      stage.id === stageId 
        ? { 
            ...stage, 
            status: 'COMPLETED', 
            progress: 100,
            endTime: new Date(),
            actualDuration: stage.startTime 
              ? Math.round((new Date().getTime() - stage.startTime.getTime()) / (1000 * 60 * 60))
              : stage.estimatedDuration
          }
        : stage
    ))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">مراحل الإنتاج</h1>
          <p className="text-gray-600">متابعة وإدارة مراحل الإنتاج المختلفة</p>
        </div>
        <Button>
          <Settings className="ml-2 h-4 w-4" />
          إعدادات المراحل
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المراحل النشطة</p>
                <p className="text-2xl font-bold text-blue-600">
                  {stages.filter(s => s.status === 'IN_PROGRESS').length}
                </p>
              </div>
              <Play className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المراحل المكتملة</p>
                <p className="text-2xl font-bold text-green-600">
                  {stages.filter(s => s.status === 'COMPLETED').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-gray-600">
                  {stages.filter(s => s.status === 'PENDING').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط التقدم</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(stages.reduce((acc, s) => acc + s.progress, 0) / stages.length)}%
                </p>
              </div>
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Production Stages Table */}
      <Card>
        <CardHeader>
          <CardTitle>مراحل الإنتاج الحالية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stages.map((stage) => (
              <div key={stage.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <Badge className={getStatusColor(stage.status)}>
                      {getStatusIcon(stage.status)}
                      <span className="mr-1">{getStatusText(stage.status)}</span>
                    </Badge>
                    <div>
                      <h3 className="font-semibold">{stage.stageName}</h3>
                      <p className="text-sm text-gray-600">
                        {stage.workOrderNumber} - {stage.productName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {stage.status === 'PENDING' && (
                      <Button 
                        size="sm" 
                        onClick={() => handleStartStage(stage.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Play className="h-4 w-4 ml-1" />
                        بدء
                      </Button>
                    )}
                    {stage.status === 'IN_PROGRESS' && (
                      <>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handlePauseStage(stage.id)}
                        >
                          <Pause className="h-4 w-4 ml-1" />
                          إيقاف
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleCompleteStage(stage.id)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <CheckCircle className="h-4 w-4 ml-1" />
                          إكمال
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">المشغل</p>
                    <p className="font-medium">{stage.operator}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الماكينة</p>
                    <p className="font-medium">{stage.machine}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المدة المقدرة</p>
                    <p className="font-medium">{stage.estimatedDuration} ساعة</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>التقدم</span>
                    <span>{stage.progress}%</span>
                  </div>
                  <Progress value={stage.progress} className="h-2" />
                </div>

                {stage.notes && (
                  <div className="mt-3 p-2 bg-gray-100 rounded text-sm">
                    <strong>ملاحظات:</strong> {stage.notes}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
