@echo off
echo.
echo Safa Textile ERP - Windows Offline Edition
echo ===========================================
echo.

REM Stop any running instances
taskkill /F /IM node.exe 2>nul
timeout /t 2 /nobreak >nul

echo [1/4] Setting up offline mode...
copy "next.config.offline.js" "next.config.js" >nul 2>&1

echo DATABASE_URL="file:./data/database.db" > .env
echo NEXTAUTH_URL="http://localhost:3000" >> .env
echo NEXTAUTH_SECRET="safa-textile-offline-2024" >> .env
echo OFFLINE_MODE="true" >> .env

if not exist "data" mkdir data

echo [2/4] Preparing database...
cmd /c "npx prisma generate" >nul 2>&1
cmd /c "npx prisma db push" >nul 2>&1

echo [3/4] Installing dependencies...
if not exist "node_modules" (
    cmd /c "npm install" >nul 2>&1
)

echo [4/4] Starting system...
echo.
echo ==========================================
echo Safa Textile ERP - Ready!
echo ==========================================
echo.
echo Mode: OFFLINE (No Internet Required)
echo Database: SQLite (Local File)
echo URL: http://localhost:3000
echo.
echo Default Login:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo Press Ctrl+C to stop
echo ==========================================
echo.

start http://localhost:3000

cmd /c "npm run dev"
