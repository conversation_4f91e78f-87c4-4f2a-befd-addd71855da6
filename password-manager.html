<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة كلمات المرور - نظام الصفوة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 800;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        
        .current-passwords {
            background: linear-gradient(135d, #fee2e2 0%, #fecaca 100%);
            border: 2px solid #ef4444;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .current-title {
            color: #991b1b;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .password-row {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            font-weight: 600;
            color: #374151;
        }
        
        .current-pass {
            font-family: monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            color: #991b1b;
        }
        
        .new-passwords {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .new-title {
            color: #065f46;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .password-input-row {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
        }
        
        .input-label {
            font-weight: 600;
            color: #047857;
            margin-bottom: 8px;
            display: block;
        }
        
        .password-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #d1fae5;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
            transition: border-color 0.3s;
        }
        
        .password-input:focus {
            outline: none;
            border-color: #10b981;
        }
        
        .suggested-passwords {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border: 2px solid #3b82f6;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .suggested-title {
            color: #1e40af;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .suggestion-row {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .suggested-pass {
            font-family: monospace;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            color: #1e40af;
        }
        
        .use-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .use-btn:hover {
            background: #2563eb;
        }
        
        .generate-section {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .generate-title {
            color: #5b21b6;
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
        }
        
        .generate-btn {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        
        .output-section {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            display: none;
        }
        
        .strength-indicator {
            margin-top: 5px;
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s;
        }
        
        .strength-weak { background: #ef4444; width: 25%; }
        .strength-medium { background: #f59e0b; width: 50%; }
        .strength-strong { background: #10b981; width: 75%; }
        .strength-very-strong { background: #059669; width: 100%; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 إدارة كلمات المرور</div>
            <div class="subtitle">نظام الصفوة للنسيج - تحديث كلمات المرور</div>
        </div>
        
        <!-- Current Passwords -->
        <div class="current-passwords">
            <div class="current-title">🔴 كلمات المرور الحالية</div>
            
            <div class="password-row">
                <div class="user-info">👑 مدير النظام</div>
                <div class="current-pass">admin123</div>
            </div>
            
            <div class="password-row">
                <div class="user-info">👨‍💼 مشرف الإنتاج</div>
                <div class="current-pass">super123</div>
            </div>
            
            <div class="password-row">
                <div class="user-info">👷‍♂️ مشغل الماكينة</div>
                <div class="current-pass">oper123</div>
            </div>
        </div>
        
        <!-- Suggested Passwords -->
        <div class="suggested-passwords">
            <div class="suggested-title">💡 كلمات مرور مقترحة</div>
            
            <div class="suggestion-row">
                <div class="user-info">👑 مدير النظام</div>
                <div class="suggested-pass">safa2024</div>
                <button class="use-btn" onclick="usePassword('admin', 'safa2024')">استخدام</button>
            </div>
            
            <div class="suggestion-row">
                <div class="user-info">👨‍💼 مشرف الإنتاج</div>
                <div class="suggested-pass">textile123</div>
                <button class="use-btn" onclick="usePassword('supervisor', 'textile123')">استخدام</button>
            </div>
            
            <div class="suggestion-row">
                <div class="user-info">👷‍♂️ مشغل الماكينة</div>
                <div class="suggested-pass">machine456</div>
                <button class="use-btn" onclick="usePassword('operator', 'machine456')">استخدام</button>
            </div>
        </div>
        
        <!-- New Passwords Input -->
        <div class="new-passwords">
            <div class="new-title">✅ كلمات المرور الجديدة</div>
            
            <div class="password-input-row">
                <label class="input-label">👑 كلمة مرور المدير الجديدة:</label>
                <input type="text" class="password-input" id="adminPass" placeholder="أدخل كلمة مرور قوية للمدير" oninput="checkStrength('adminPass', 'adminStrength')">
                <div class="strength-indicator" id="adminStrength"></div>
            </div>
            
            <div class="password-input-row">
                <label class="input-label">👨‍💼 كلمة مرور المشرف الجديدة:</label>
                <input type="text" class="password-input" id="supervisorPass" placeholder="أدخل كلمة مرور للمشرف" oninput="checkStrength('supervisorPass', 'supervisorStrength')">
                <div class="strength-indicator" id="supervisorStrength"></div>
            </div>
            
            <div class="password-input-row">
                <label class="input-label">👷‍♂️ كلمة مرور المشغل الجديدة:</label>
                <input type="text" class="password-input" id="operatorPass" placeholder="أدخل كلمة مرور للمشغل" oninput="checkStrength('operatorPass', 'operatorStrength')">
                <div class="strength-indicator" id="operatorStrength"></div>
            </div>
        </div>
        
        <!-- Password Generator -->
        <div class="generate-section">
            <div class="generate-title">🎲 مولد كلمات المرور</div>
            <button class="generate-btn" onclick="generatePassword('strong')">كلمة مرور قوية</button>
            <button class="generate-btn" onclick="generatePassword('arabic')">كلمة مرور عربية</button>
            <button class="generate-btn" onclick="generatePassword('simple')">كلمة مرور بسيطة</button>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-success" onclick="generateConfig()">📄 إنشاء ملف التحديث</button>
            <button class="btn btn-warning" onclick="useDefaults()">🔄 استخدام الافتراضية</button>
            <button class="btn" onclick="showInstructions()">📋 عرض التعليمات</button>
        </div>
        
        <!-- Output Section -->
        <div class="output-section" id="outputSection">
            <div id="outputContent"></div>
        </div>
    </div>
    
    <script>
        function usePassword(user, password) {
            const inputId = user === 'admin' ? 'adminPass' : 
                           user === 'supervisor' ? 'supervisorPass' : 'operatorPass';
            document.getElementById(inputId).value = password;
            
            const strengthId = user === 'admin' ? 'adminStrength' : 
                              user === 'supervisor' ? 'supervisorStrength' : 'operatorStrength';
            checkStrength(inputId, strengthId);
        }
        
        function generatePassword(type) {
            let password = '';
            
            if (type === 'strong') {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%';
                for (let i = 0; i < 12; i++) {
                    password += chars.charAt(Math.floor(Math.random() * chars.length));
                }
            } else if (type === 'arabic') {
                const words = ['الصفوة', 'نسيج', 'انتاج', 'جودة', 'ماكينة'];
                const numbers = Math.floor(Math.random() * 999) + 100;
                password = words[Math.floor(Math.random() * words.length)] + numbers;
            } else if (type === 'simple') {
                const words = ['safa', 'textile', 'fabric', 'quality'];
                const numbers = Math.floor(Math.random() * 999) + 100;
                password = words[Math.floor(Math.random() * words.length)] + numbers;
            }
            
            // عرض كلمة المرور المولدة
            alert('كلمة المرور المولدة: ' + password);
        }
        
        function checkStrength(inputId, strengthId) {
            const password = document.getElementById(inputId).value;
            const strengthBar = document.getElementById(strengthId);
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            strengthBar.className = 'strength-indicator';
            if (strength <= 1) strengthBar.classList.add('strength-weak');
            else if (strength <= 2) strengthBar.classList.add('strength-medium');
            else if (strength <= 3) strengthBar.classList.add('strength-strong');
            else strengthBar.classList.add('strength-very-strong');
        }
        
        function useDefaults() {
            document.getElementById('adminPass').value = 'safa2024';
            document.getElementById('supervisorPass').value = 'textile123';
            document.getElementById('operatorPass').value = 'machine456';
            
            checkStrength('adminPass', 'adminStrength');
            checkStrength('supervisorPass', 'supervisorStrength');
            checkStrength('operatorPass', 'operatorStrength');
        }
        
        function generateConfig() {
            const adminPass = document.getElementById('adminPass').value || 'safa2024';
            const supervisorPass = document.getElementById('supervisorPass').value || 'textile123';
            const operatorPass = document.getElementById('operatorPass').value || 'machine456';
            
            const config = `
// تحديث كلمات المرور - نظام الصفوة
const newPasswords = {
  admin: '${adminPass}',
  supervisor: '${supervisorPass}',
  operator: '${operatorPass}'
};

console.log('🔐 كلمات المرور الجديدة:');
console.log('مدير النظام: <EMAIL> / ${adminPass}');
console.log('مشرف الإنتاج: <EMAIL> / ${supervisorPass}');
console.log('مشغل الماكينة: <EMAIL> / ${operatorPass}');
`;
            
            document.getElementById('outputContent').textContent = config;
            document.getElementById('outputSection').style.display = 'block';
            
            // إنشاء ملف للتحميل
            const blob = new Blob([config], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'new-passwords.js';
            a.click();
        }
        
        function showInstructions() {
            const instructions = `
📋 تعليمات تحديث كلمات المرور:

1. اختر كلمات مرور جديدة أو استخدم المقترحة
2. اضغط "إنشاء ملف التحديث"
3. شغل الملف: update-passwords.bat
4. انتظر حتى إعادة تشغيل النظام
5. استخدم كلمات المرور الجديدة للدخول

⚠️ تأكد من حفظ كلمات المرور الجديدة!
            `;
            
            alert(instructions);
        }
        
        // تحميل الافتراضية عند فتح الصفحة
        window.onload = function() {
            useDefaults();
        };
    </script>
</body>
</html>
