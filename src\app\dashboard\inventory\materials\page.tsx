import { Suspense } from 'react'
import { MaterialsList } from '@/components/inventory/materials-list'
import { MaterialsHeader } from '@/components/inventory/materials-header'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function MaterialsPage() {
  return (
    <div className="space-y-6">
      <MaterialsHeader />
      
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<MaterialsListSkeleton />}>
            <MaterialsList />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  )
}

function MaterialsListSkeleton() {
  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-4 w-[200px]" />
            </div>
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        ))}
      </div>
    </div>
  )
}
