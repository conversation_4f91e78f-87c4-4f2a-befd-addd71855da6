# دليل التطوير المتقدم - نظام إدارة النسيج

## 🎯 الحالة الحالية للتطوير

### ✅ الوحدات المكتملة

#### 1. وحدة المخزون (Inventory Module)
- **المواد الخام**: `/dashboard/inventory/materials`
  - عرض قائمة المواد مع تفاصيل كاملة
  - إضافة مواد جديدة مع التحقق من البيانات
  - تصفية حسب النوع والبحث
  - تنبيهات المخزون المنخفض والحرج
  - ربط بالموردين

- **المنتجات**: `/dashboard/inventory/products`
  - عرض قائمة المنتجات مع التفاصيل
  - إضافة منتجات جديدة
  - حساب هامش الربح تلقائياً
  - تصفية حسب الفئة
  - تتبع المخزون والحد الأدنى

#### 2. وحدة الإنتاج (Production Module)
- **أوامر العمل**: `/dashboard/production/work-orders`
  - عرض أوامر العمل مع الحالة والتقدم
  - إنشاء أوامر عمل جديدة
  - تحديد الأولويات والمسؤولين
  - إحصائيات شاملة للأوامر
  - تتبع التواريخ والمواعيد النهائية

### 🚧 الوحدات قيد التطوير

#### 3. وحدة المشتريات (Purchasing Module)
- **الموردين**: إدارة بيانات الموردين
- **أوامر الشراء**: إنشاء ومتابعة أوامر الشراء
- **طلبات الشراء**: نظام طلبات الشراء الداخلية

#### 4. وحدة المبيعات (Sales Module)
- **العملاء**: إدارة بيانات العملاء
- **أوامر البيع**: إنشاء ومتابعة أوامر البيع
- **عروض الأسعار**: نظام عروض الأسعار

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 14**: إطار عمل React مع App Router
- **TypeScript**: للتحقق من الأنواع
- **Tailwind CSS**: للتصميم مع دعم RTL
- **Radix UI**: مكونات واجهة المستخدم
- **React Hook Form**: إدارة النماذج
- **Zod**: التحقق من البيانات
- **Recharts**: الرسوم البيانية

### Backend
- **Prisma ORM**: للتعامل مع قاعدة البيانات
- **PostgreSQL**: قاعدة البيانات الرئيسية
- **NextAuth.js**: نظام المصادقة
- **bcryptjs**: تشفير كلمات المرور

### أدوات التطوير
- **ESLint**: فحص الكود
- **TypeScript**: التحقق من الأنواع
- **Prisma Studio**: إدارة قاعدة البيانات

## 📁 هيكل المشروع

```
src/
├── app/                          # صفحات Next.js (App Router)
│   ├── dashboard/               # صفحات لوحة التحكم
│   │   ├── inventory/          # وحدة المخزون
│   │   │   ├── materials/      # المواد الخام
│   │   │   └── products/       # المنتجات
│   │   ├── production/         # وحدة الإنتاج
│   │   │   └── work-orders/    # أوامر العمل
│   │   └── layout.tsx          # تخطيط لوحة التحكم
│   ├── api/                    # API Routes
│   │   └── auth/              # مسارات المصادقة
│   ├── globals.css            # الأنماط العامة
│   ├── layout.tsx             # التخطيط الرئيسي
│   └── page.tsx               # الصفحة الرئيسية
├── components/                 # مكونات واجهة المستخدم
│   ├── ui/                    # مكونات أساسية
│   ├── layout/                # مكونات التخطيط
│   ├── dashboard/             # مكونات لوحة التحكم
│   ├── inventory/             # مكونات المخزون
│   ├── production/            # مكونات الإنتاج
│   └── providers.tsx          # موفرو السياق
├── lib/                       # مكتبات مساعدة
│   ├── auth.ts               # إعدادات المصادقة
│   ├── prisma.ts             # إعدادات Prisma
│   └── utils.ts              # وظائف مساعدة
├── types/                     # تعريفات TypeScript
└── hooks/                     # React Hooks مخصصة
```

## 🔧 إعداد بيئة التطوير

### 1. متطلبات النظام
- Node.js 18+ 
- PostgreSQL 14+
- Git

### 2. تثبيت المشروع
```bash
# استنساخ المشروع
git clone <repository-url>
cd textile-erp

# تثبيت المكتبات
npm install

# إعداد متغيرات البيئة
cp .env.example .env
# قم بتعديل .env بمعلومات قاعدة البيانات

# إعداد قاعدة البيانات
npx prisma migrate dev
npx prisma db seed

# تشغيل الخادم
npm run dev
```

### 3. أوامر مفيدة للتطوير
```bash
# تشغيل وضع التطوير
npm run dev

# بناء المشروع
npm run build

# فحص الكود
npm run lint

# إدارة قاعدة البيانات
npm run db:studio      # فتح Prisma Studio
npm run db:migrate     # تطبيق migrations
npm run db:seed        # إضافة بيانات تجريبية
npm run db:reset       # إعادة تعيين قاعدة البيانات
```

## 📝 إرشادات التطوير

### 1. إضافة صفحة جديدة
```typescript
// src/app/dashboard/new-module/page.tsx
export default function NewModulePage() {
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">الوحدة الجديدة</h1>
      {/* محتوى الصفحة */}
    </div>
  )
}
```

### 2. إنشاء مكون جديد
```typescript
// src/components/module/component-name.tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

interface ComponentProps {
  // تعريف الخصائص
}

export function ComponentName({ }: ComponentProps) {
  return (
    <div>
      {/* محتوى المكون */}
    </div>
  )
}
```

### 3. إضافة نموذج جديد لقاعدة البيانات
```prisma
// prisma/schema.prisma
model NewModel {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("new_models")
}
```

### 4. إنشاء نموذج (Form) جديد
```typescript
// استخدام react-hook-form مع zod
const schema = z.object({
  name: z.string().min(1, 'الاسم مطلوب'),
  // باقي الحقول
})

const form = useForm({
  resolver: zodResolver(schema),
  defaultValues: {
    name: '',
  },
})
```

## 🎨 إرشادات التصميم

### 1. الألوان والثيمات
- استخدم متغيرات CSS المعرفة في `globals.css`
- اتبع نظام الألوان الموحد للحالات (أخضر للنجاح، أحمر للخطأ، إلخ)

### 2. التخطيط العربي (RTL)
- جميع المكونات تدعم RTL تلقائياً
- استخدم `text-right` للنصوص العربية
- استخدم `ml-2` بدلاً من `mr-2` للمسافات

### 3. الاستجابة (Responsive)
- استخدم Tailwind breakpoints: `sm:`, `md:`, `lg:`, `xl:`
- اختبر على جميع أحجام الشاشات

## 🧪 الاختبار

### 1. اختبار المكونات
```bash
# سيتم إضافة Jest و React Testing Library لاحقاً
npm run test
```

### 2. اختبار قاعدة البيانات
```bash
# اختبار الاتصال
npx prisma db push

# فحص البيانات
npm run db:studio
```

## 🚀 النشر

### 1. بناء الإنتاج
```bash
npm run build
npm start
```

### 2. متغيرات البيئة للإنتاج
```env
NODE_ENV=production
DATABASE_URL="postgresql://..."
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="production-secret"
```

## 📋 المهام القادمة

### الأولوية العالية
1. **إكمال وحدة المشتريات**
   - صفحة الموردين
   - صفحة أوامر الشراء
   - ربط بالمخزون

2. **إكمال وحدة المبيعات**
   - صفحة العملاء
   - صفحة أوامر البيع
   - ربط بالمخزون

3. **تطوير API endpoints**
   - CRUD operations لجميع الوحدات
   - التحقق من البيانات
   - معالجة الأخطاء

### الأولوية المتوسطة
1. **وحدة التقارير**
   - تقارير المخزون
   - تقارير الإنتاج
   - تقارير المبيعات

2. **تحسين الأداء**
   - تحسين الاستعلامات
   - Lazy loading
   - Caching

### الأولوية المنخفضة
1. **ميزات متقدمة**
   - نظام الإشعارات
   - تطبيق موبايل
   - ربط IoT

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/new-feature`)
3. Commit التغييرات (`git commit -m 'Add new feature'`)
4. Push إلى Branch (`git push origin feature/new-feature`)
5. إنشاء Pull Request

## 📞 الدعم

للحصول على المساعدة:
- راجع الوثائق في هذا الملف
- تحقق من Issues في GitHub
- تواصل مع فريق التطوير
