@echo off
echo.
echo Creating All Missing Modules - Safa Textile ERP
echo ===============================================
echo.

echo [1/15] Creating Production Stages module...
if not exist "src\app\dashboard\production\stages" mkdir "src\app\dashboard\production\stages"

echo [2/15] Creating Production Planning module...
if not exist "src\app\dashboard\production\planning" mkdir "src\app\dashboard\production\planning"

echo [3/15] Creating Inventory Movements module...
if not exist "src\app\dashboard\inventory\movements" mkdir "src\app\dashboard\inventory\movements"

echo [4/15] Creating Inventory Count module...
if not exist "src\app\dashboard\inventory\count" mkdir "src\app\dashboard\inventory\count"

echo [5/15] Creating Purchase Requests module...
if not exist "src\app\dashboard\purchasing\requests" mkdir "src\app\dashboard\purchasing\requests"

echo [6/15] Creating Sales Quotations module...
if not exist "src\app\dashboard\sales\quotes" mkdir "src\app\dashboard\sales\quotes"

echo [7/15] Creating Finance Accounts module...
if not exist "src\app\dashboard\finance\accounts" mkdir "src\app\dashboard\finance\accounts"

echo [8/15] Creating Finance Invoices module...
if not exist "src\app\dashboard\finance\invoices" mkdir "src\app\dashboard\finance\invoices"

echo [9/15] Creating Finance Reports module...
if not exist "src\app\dashboard\finance\reports" mkdir "src\app\dashboard\finance\reports"

echo [10/15] Creating HR Attendance module...
if not exist "src\app\dashboard\hr\attendance" mkdir "src\app\dashboard\hr\attendance"

echo [11/15] Creating HR Payroll module...
if not exist "src\app\dashboard\hr\payroll" mkdir "src\app\dashboard\hr\payroll"

echo [12/15] Creating Quality module...
if not exist "src\app\dashboard\quality" mkdir "src\app\dashboard\quality"

echo [13/15] Creating Maintenance module...
if not exist "src\app\dashboard\maintenance" mkdir "src\app\dashboard\maintenance"

echo [14/15] Creating User Management module...
if not exist "src\app\dashboard\users" mkdir "src\app\dashboard\users"

echo [15/15] Creating Settings module...
if not exist "src\app\dashboard\settings" mkdir "src\app\dashboard\settings"

echo.
echo ===============================================
echo All module directories created successfully!
echo ===============================================
echo.
echo Next steps:
echo 1. Module pages are being created
echo 2. Database schema will be updated
echo 3. Navigation will be updated
echo 4. System will be restarted
echo.
echo Please wait while the system completes setup...
echo.

REM Restart the development server
taskkill /F /IM node.exe 2>nul
timeout /t 3 /nobreak >nul

echo Starting updated system...
start http://localhost:3000
cmd /c "npm run dev"
