import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { RecentOrders } from '@/components/dashboard/recent-orders'
import { ProductionChart } from '@/components/dashboard/production-chart'
import { InventoryAlerts } from '@/components/dashboard/inventory-alerts'

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/')
  }

  return (
    <div className="space-y-8">
      {/* رأس لوحة التحكم العصري */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-8 text-white shadow-2xl">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">لوحة التحكم الرئيسية</h1>
            <p className="text-blue-100 text-lg">نظام إدارة موارد المؤسسة - شركة الصفوة</p>
          </div>
          <div className="text-right">
            <div className="bg-white/20 backdrop-blur-lg rounded-xl p-4">
              <p className="text-sm text-blue-100">مرحباً بك</p>
              <p className="text-xl font-bold">{session.user.name}</p>
              <p className="text-sm text-blue-200">{session.user.role}</p>
            </div>
          </div>
        </div>

        {/* مؤشرات سريعة */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 text-center">
            <div className="text-2xl font-bold">156</div>
            <div className="text-sm text-blue-200">إجمالي الموظفين</div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 text-center">
            <div className="text-2xl font-bold">24</div>
            <div className="text-sm text-blue-200">أوامر العمل النشطة</div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 text-center">
            <div className="text-2xl font-bold">89%</div>
            <div className="text-sm text-blue-200">معدل الجودة</div>
          </div>
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-4 text-center">
            <div className="text-2xl font-bold">485K</div>
            <div className="text-sm text-blue-200">المبيعات الشهرية</div>
          </div>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <DashboardStats />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الطلبات الحديثة */}
        <RecentOrders />

        {/* تنبيهات المخزون */}
        <InventoryAlerts />
      </div>

      {/* مخطط الإنتاج */}
      <ProductionChart />
    </div>
  )
}
