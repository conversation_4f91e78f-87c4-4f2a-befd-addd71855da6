import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { RecentOrders } from '@/components/dashboard/recent-orders'
import { ProductionChart } from '@/components/dashboard/production-chart'
import { InventoryAlerts } from '@/components/dashboard/inventory-alerts'

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">لوحة التحكم</h1>
        <div className="text-sm text-muted-foreground">
          مرحباً، {session.user.name}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <DashboardStats />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الطلبات الحديثة */}
        <RecentOrders />

        {/* تنبيهات المخزون */}
        <InventoryAlerts />
      </div>

      {/* مخطط الإنتاج */}
      <ProductionChart />
    </div>
  )
}
