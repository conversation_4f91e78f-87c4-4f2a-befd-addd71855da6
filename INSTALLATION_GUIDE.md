# 📦 دليل تثبيت نظام الصفوة للنسيج

## 🎯 **طرق التثبيت المتاحة:**

### **1. المثبت الاحترافي (موصى به)**
### **2. النسخة المحمولة**
### **3. التثبيت اليدوي**

---

## 🏆 **الطريقة الأولى: المثبت الاحترافي**

### **إنشاء المثبت:**
```cmd
# شغل الملف:
.\create-installer.bat
```

### **المميزات:**
- ✅ **مثبت Windows احترافي**
- ✅ **اختصارات سطح المكتب وقائمة ابدأ**
- ✅ **إمكانية اختيار مجلد التثبيت**
- ✅ **إلغاء تثبيت آمن**
- ✅ **تحديثات تلقائية (مستقبلاً)**

### **النتيجة:**
- **ملف EXE** في مجلد `dist`
- **حجم تقريبي:** 200-300 MB
- **يعمل على أي Windows** بدون متطلبات

### **طريقة التوزيع:**
1. **انسخ ملف EXE** من مجلد dist
2. **وزعه على الأجهزة** المطلوبة
3. **شغل الملف** واتبع التعليمات
4. **ابحث عن الاختصار** في سطح المكتب

---

## 🎒 **الطريقة الثانية: النسخة المحمولة**

### **إنشاء النسخة المحمولة:**
```cmd
# شغل الملف:
.\create-portable-version.bat
```

### **المميزات:**
- ✅ **تعمل من أي مجلد**
- ✅ **لا تحتاج تثبيت**
- ✅ **سهلة النقل والتوزيع**
- ✅ **تعمل من USB أو قرص خارجي**
- ✅ **لا تترك أثر في النظام**

### **النتيجة:**
- **مجلد:** `Safa-Textile-Portable`
- **ملف مضغوط:** `Safa-Textile-Portable.zip`
- **حجم تقريبي:** 50-100 MB

### **طريقة الاستخدام:**
1. **فك ضغط الملف** في أي مكان
2. **شغل:** `تثبيت-سريع.bat` (أول مرة فقط)
3. **شغل:** `تشغيل-نظام-الصفوة.bat`
4. **سجل دخول:** admin / admin123

---

## 🔧 **الطريقة الثالثة: التثبيت اليدوي**

### **للمطورين والمستخدمين المتقدمين:**

#### **الخطوة 1 - تحضير البيئة:**
```cmd
# تأكد من Node.js 18+
node --version

# انتقل لمجلد المشروع
cd "C:\Users\<USER>\Desktop\الصفوه\1003"
```

#### **الخطوة 2 - تثبيت المكتبات:**
```cmd
# ثبت المكتبات
npm install

# ثبت Electron
npm install electron --save-dev
```

#### **الخطوة 3 - التشغيل:**
```cmd
# للتشغيل العادي:
npm run dev

# للتشغيل كسطح مكتب:
npx electron .
```

---

## 📊 **مقارنة طرق التثبيت:**

| الميزة | المثبت الاحترافي | النسخة المحمولة | التثبيت اليدوي |
|--------|------------------|------------------|------------------|
| **سهولة التثبيت** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| **سهولة التوزيع** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐ |
| **حجم الملف** | كبير | متوسط | صغير |
| **المتطلبات** | لا يحتاج | Node.js | Node.js |
| **الاحترافية** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **المرونة** | ⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

---

## 🎯 **التوصيات حسب الاستخدام:**

### **للشركات والاستخدام الرسمي:**
**استخدم المثبت الاحترافي**
- مظهر احترافي
- سهولة التوزيع
- اختصارات تلقائية
- إلغاء تثبيت آمن

### **للعروض التوضيحية والتجريب:**
**استخدم النسخة المحمولة**
- سرعة في التشغيل
- لا تحتاج صلاحيات إدارية
- سهولة النقل
- لا تترك أثر

### **للتطوير والتخصيص:**
**استخدم التثبيت اليدوي**
- مرونة كاملة
- إمكانية التعديل
- تحديثات فورية
- تحكم كامل

---

## 🔧 **متطلبات النظام:**

### **الحد الأدنى:**
- **نظام التشغيل:** Windows 10
- **المعالج:** Intel/AMD 2GHz
- **الذاكرة:** 4GB RAM
- **مساحة القرص:** 2GB
- **الشاشة:** 1024x768

### **الموصى به:**
- **نظام التشغيل:** Windows 11
- **المعالج:** Intel/AMD 3GHz+
- **الذاكرة:** 8GB RAM
- **مساحة القرص:** 5GB
- **الشاشة:** 1920x1080

---

## 📋 **خطوات ما بعد التثبيت:**

### **1. التشغيل الأول:**
- **افتح النظام**
- **سجل دخول:** admin / admin123
- **استكشف الوحدات**

### **2. إعداد البيانات:**
- **راجع البيانات التجريبية**
- **أضف بياناتك الخاصة**
- **اختبر جميع الوحدات**

### **3. التدريب:**
- **تدرب على الواجهة**
- **جرب جميع الميزات**
- **اقرأ الأدلة المرفقة**

---

## 🆘 **استكشاف أخطاء التثبيت:**

### **إذا فشل إنشاء المثبت:**
```cmd
# تحقق من Node.js:
node --version

# أعد تثبيت المكتبات:
rmdir /s /q node_modules
npm install

# شغل كمدير:
# اضغط Windows + X → Windows PowerShell (Admin)
```

### **إذا لم تعمل النسخة المحمولة:**
```cmd
# تأكد من Node.js:
node --version

# إذا لم يكن مثبت، حمله من:
# https://nodejs.org
```

### **إذا ظهرت أخطاء أثناء التشغيل:**
```cmd
# نظف الكاش:
npm cache clean --force

# أعد تثبيت المكتبات:
npm install

# أعد المحاولة
```

---

## 📞 **الدعم الفني:**

### **للمساعدة في التثبيت:**
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** www.safa-textile.com
- **الهاتف:** +966-XX-XXX-XXXX

### **المعلومات المطلوبة عند طلب المساعدة:**
- **نوع التثبيت المستخدم**
- **نظام التشغيل وإصداره**
- **رسالة الخطأ الكاملة**
- **خطوات إعادة إنتاج المشكلة**

---

## 🎉 **النتيجة النهائية:**

### **بعد التثبيت الناجح ستحصل على:**
- 🖥️ **نظام ERP متكامل** لإدارة مصانع النسيج
- 🏭 **جميع الوحدات** مكتملة ومتاحة
- 🔒 **يعمل بدون إنترنت** تماماً
- 🎨 **واجهة احترافية** مع شعار الصفوة
- 📊 **بيانات تجريبية** للتدريب والاختبار
- ⚡ **أداء فائق** وسرعة عالية

### **يمكنك:**
- 📈 **إدارة جميع عمليات النسيج**
- 📊 **متابعة الإنتاج والمخزون**
- 💰 **إدارة المالية والحسابات**
- 👥 **إدارة الموارد البشرية**
- 📋 **إنشاء التقارير المفصلة**

**🚀 اختر طريقة التثبيت المناسبة لك وابدأ الآن!** 🎊
