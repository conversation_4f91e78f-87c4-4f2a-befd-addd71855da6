'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { Loader2 } from 'lucide-react'

const productSchema = z.object({
  code: z.string().min(1, 'كود المنتج مطلوب'),
  nameAr: z.string().min(1, 'الاسم العربي مطلوب'),
  nameEn: z.string().optional(),
  description: z.string().optional(),
  category: z.string().min(1, 'الفئة مطلوبة'),
  unit: z.string().min(1, 'وحدة القياس مطلوبة'),
  minStock: z.number().min(0, 'الحد الأدنى يجب أن يكون أكبر من أو يساوي 0'),
  sellingPrice: z.number().min(0, 'سعر البيع يجب أن يكون أكبر من 0'),
  costPrice: z.number().min(0, 'سعر التكلفة يجب أن يكون أكبر من أو يساوي 0'),
})

type ProductFormData = z.infer<typeof productSchema>

interface AddProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const categories = [
  'قمصان',
  'بناطيل',
  'فساتين',
  'ملابس داخلية',
  'إكسسوارات',
  'أحذية',
  'حقائب',
  'أخرى'
]

export function AddProductDialog({ open, onOpenChange }: AddProductDialogProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      code: '',
      nameAr: '',
      nameEn: '',
      description: '',
      category: '',
      unit: 'قطعة',
      minStock: 0,
      sellingPrice: 0,
      costPrice: 0,
    },
  })

  const onSubmit = async (data: ProductFormData) => {
    setIsLoading(true)
    
    try {
      // هنا سيتم إرسال البيانات إلى API
      console.log('Product data:', data)
      
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'تم إضافة المنتج بنجاح',
        description: `تم إضافة ${data.nameAr} إلى قائمة المنتجات`,
      })
      
      form.reset()
      onOpenChange(false)
    } catch (error) {
      toast({
        title: 'خطأ في إضافة المنتج',
        description: 'حدث خطأ أثناء إضافة المنتج. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // حساب هامش الربح
  const sellingPrice = form.watch('sellingPrice')
  const costPrice = form.watch('costPrice')
  const marginPercentage = sellingPrice > 0 ? ((sellingPrice - costPrice) / sellingPrice * 100).toFixed(1) : '0'

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>إضافة منتج جديد</DialogTitle>
          <DialogDescription>
            أدخل تفاصيل المنتج الجديد لإضافته إلى المخزون
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>كود المنتج *</FormLabel>
                    <FormControl>
                      <Input placeholder="PRD001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الفئة *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الفئة" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="nameAr"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الاسم العربي *</FormLabel>
                    <FormControl>
                      <Input placeholder="قميص قطني أزرق" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="nameEn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الاسم الإنجليزي</FormLabel>
                    <FormControl>
                      <Input placeholder="Blue Cotton Shirt" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الوصف</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="وصف المنتج وخصائصه..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="unit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>وحدة القياس *</FormLabel>
                    <FormControl>
                      <Input placeholder="قطعة" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="minStock"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الحد الأدنى للمخزون *</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="50"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="costPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>سعر التكلفة (ريال) *</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="75.00"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sellingPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>سعر البيع (ريال) *</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        step="0.01"
                        placeholder="120.00"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <label className="text-sm font-medium">هامش الربح</label>
                <div className="h-10 px-3 py-2 border rounded-md bg-muted flex items-center">
                  <span className="text-sm font-medium text-green-600">
                    {marginPercentage}%
                  </span>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  'حفظ المنتج'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
