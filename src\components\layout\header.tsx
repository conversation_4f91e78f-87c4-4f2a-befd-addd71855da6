'use client'

import { signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Bell, LogOut, Settings, User, Search, Sun, Moon } from 'lucide-react'
import { getInitials } from '@/lib/utils'
import { Logo } from '@/components/ui/logo'

interface HeaderProps {
  user: {
    name: string
    email: string
    role: string
    department: string
  }
}

export function Header({ user }: HeaderProps) {
  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  return (
    <header className="bg-white/95 backdrop-blur-md border-b border-gray-200 px-6 py-4 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-6">
          {/* شعار الصفوة المصغر */}
          <Logo size="sm" variant="icon" />
          <div className="hidden md:block">
            <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              نظام إدارة موارد المؤسسة
            </h1>
            <p className="text-xs text-gray-500 font-medium">شركة الصفوة للنسيج</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* شريط البحث */}
          <div className="hidden md:flex items-center gap-2 bg-gray-50 rounded-xl px-4 py-2 min-w-[300px]">
            <Search className="h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في النظام..."
              className="bg-transparent border-none outline-none text-sm flex-1 text-gray-700 placeholder-gray-400"
            />
          </div>

          {/* الإشعارات */}
          <div className="relative">
            <Button variant="ghost" size="icon" className="relative hover:bg-blue-50 rounded-xl">
              <Bell className="h-5 w-5 text-gray-600" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center p-0">
                3
              </Badge>
            </Button>
          </div>

          {/* تبديل الوضع */}
          <Button variant="ghost" size="icon" className="hover:bg-blue-50 rounded-xl">
            <Sun className="h-5 w-5 text-gray-600" />
          </Button>

          {/* قائمة المستخدم */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-xl hover:bg-blue-50">
                <Avatar className="h-9 w-9 ring-2 ring-blue-100">
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                    {getInitials(user.name)}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64 rounded-xl shadow-xl border-0 bg-white/95 backdrop-blur-md" align="end" forceMount>
              <DropdownMenuLabel className="font-normal p-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-blue-100">
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-bold text-lg">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-semibold text-gray-900">{user.name}</p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                    <Badge variant="secondary" className="text-xs w-fit bg-blue-100 text-blue-700 hover:bg-blue-100">
                      {user.role}
                    </Badge>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-gray-100" />
              <DropdownMenuItem className="p-3 hover:bg-blue-50 rounded-lg mx-2 my-1">
                <User className="ml-2 h-4 w-4 text-blue-600" />
                <span className="font-medium">الملف الشخصي</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="p-3 hover:bg-blue-50 rounded-lg mx-2 my-1">
                <Settings className="ml-2 h-4 w-4 text-blue-600" />
                <span className="font-medium">الإعدادات</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-gray-100" />
              <DropdownMenuItem
                onClick={handleSignOut}
                className="p-3 hover:bg-red-50 rounded-lg mx-2 my-1 text-red-600"
              >
                <LogOut className="ml-2 h-4 w-4" />
                <span className="font-medium">تسجيل الخروج</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}
